name: 🚀 Deploy
on:
    push:
        branches:
            - main

concurrency:
    group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
    cancel-in-progress: true

permissions:
    pages: write
    contents: write

jobs:
    deploy:
        name: 🚀 Deploy
        runs-on: ubuntu-latest
        timeout-minutes: 10

        env:
            SUPABASE_ACCESS_TOKEN: ${{ secrets.SUPABASE_ACCESS_TOKEN }}
            SUPABASE_DB_PASSWORD: ${{ secrets.SUPABASE_DB_PASSWORD }}
            SUPABASE_PROJECT_ID: ${{ secrets.SUPABASE_PROJECT_ID }}
            VITE_SUPABASE_URL: ${{ secrets.SUPABASE_URL }}
            VITE_SUPABASE_ANON_KEY: ${{ secrets.SUPABASE_ANON_KEY }}
            IS_SUPABASE_CONFIGURED: ${{ secrets.SUPABASE_ACCESS_TOKEN && secrets.SUPABASE_DB_PASSWORD && secrets.SUPABASE_PROJECT_ID && secrets.SUPABASE_URL && secrets.SUPABASE_ANON_KEY }}
            VITE_IS_DEMO: ${{ vars.VITE_IS_DEMO }}
            VITE_INBOUND_EMAIL: ${{ vars.VITE_INBOUND_EMAIL }}
            PRODUCTION_REMOTE: https://git:${{ secrets.DEPLOY_TOKEN || secrets.GITHUB_TOKEN }}@github.com/${{ vars.DEPLOY_REPOSITORY || github.repository }}
            POSTMARK_WEBHOOK_USER: ${{secrets.POSTMARK_WEBHOOK_USER}}
            POSTMARK_WEBHOOK_PASSWORD: ${{secrets.POSTMARK_WEBHOOK_PASSWORD}}
            POSTMARK_WEBHOOK_AUTHORIZED_IPS: ${{secrets.POSTMARK_WEBHOOK_AUTHORIZED_IPS}}

        steps:
            - name: 📥 Checkout repo
              uses: actions/checkout@v4

            - name: ⚙️ Setup node
              uses: actions/setup-node@v4
              with:
                  node-version: 20
                  cache: npm

            - name: ⚙️ Setup git
              run: |
                  echo Deploying to pages to $PRODUCTION_REMOTE
                  git remote set-url origin https://git:${{ secrets.GITHUB_TOKEN }}@github.com/${GITHUB_REPOSITORY}
                  git remote add production $PRODUCTION_REMOTE
                  git config --global user.email <EMAIL>
                  git config --global user.name github-actions-bot

            - name: ⚙️ Setup supabase
              uses: supabase/setup-cli@v1

            - name: 📥 Download deps
              run: npm install

            - name: 🔨 Build
              run: npm run build
            
            - if: ${{ env.IS_SUPABASE_CONFIGURED }}
              name: 🔗 Supabase Link
              run: npx supabase link --project-ref $SUPABASE_PROJECT_ID

            - if: ${{ env.IS_SUPABASE_CONFIGURED }}
              name: 📡 Push supabase migrations
              run: npx supabase db push

            - if: ${{ env.IS_SUPABASE_CONFIGURED && env.POSTMARK_WEBHOOK_USER && env.POSTMARK_WEBHOOK_PASSWORD && env.POSTMARK_WEBHOOK_AUTHORIZED_IPS }}
              name: 📡 Push supabase postmark webhook user secret
              run: |
                  npx supabase secrets set POSTMARK_WEBHOOK_USER=${{ env.POSTMARK_WEBHOOK_USER }}
                  npx supabase secrets set POSTMARK_WEBHOOK_PASSWORD=${{ env.POSTMARK_WEBHOOK_PASSWORD }}
                  npx supabase secrets set POSTMARK_WEBHOOK_AUTHORIZED_IPS=${{ env.POSTMARK_WEBHOOK_AUTHORIZED_IPS }}

            - if: ${{ env.IS_SUPABASE_CONFIGURED }}
              name: 📡 Deploy supabase functions
              run: npx supabase functions deploy

            - if: ${{ !env.SUPABASE_ACCESS_TOKEN }}
              name: Check SUPABASE_ACCESS_TOKEN secret
              run: echo ':warning:SUPABASE_ACCESS_TOKEN secret is missing' >> $GITHUB_STEP_SUMMARY

            - if: ${{ !env.SUPABASE_DB_PASSWORD }}
              name: Check SUPABASE_DB_PASSWORD secret
              run: echo ':warning:SUPABASE_DB_PASSWORD secret is missing' >> $GITHUB_STEP_SUMMARY

            - if: ${{ !env.SUPABASE_PROJECT_ID }}
              name: Check SUPABASE_PROJECT_ID secret
              run: echo ':warning:SUPABASE_PROJECT_ID secret is missing' >> $GITHUB_STEP_SUMMARY

            - if: ${{ !env.VITE_SUPABASE_URL }}
              name: Check SUPABASE_URL secret
              run: echo ':warning:SUPABASE_URL secret is missing' >> $GITHUB_STEP_SUMMARY

            - if: ${{ !env.VITE_SUPABASE_ANON_KEY }}
              name: Check SUPABASE_ANON_KEY secret
              run: echo ':warning:SUPABASE_ANON_KEY secret is missing' >> $GITHUB_STEP_SUMMARY

            - if: ${{ !env.POSTMARK_WEBHOOK_USER }}
              name: Check POSTMARK_WEBHOOK_USER secret
              run: echo ':warning:POSTMARK_WEBHOOK_USER secret is missing' >> $GITHUB_STEP_SUMMARY

            - if: ${{ !env.POSTMARK_WEBHOOK_PASSWORD }}
              name: Check POSTMARK_WEBHOOK_USER secret
              run: echo ':warning:POSTMARK_WEBHOOK_PASSWORD secret is missing' >> $GITHUB_STEP_SUMMARY

            - if: ${{ !env.POSTMARK_WEBHOOK_AUTHORIZED_IPS }}
              name: Check POSTMARK_WEBHOOK_USER secret
              run: echo ':warning:POSTMARK_WEBHOOK_AUTHORIZED_IPS secret is missing' >> $GITHUB_STEP_SUMMARY

            - if: ${{ !env.IS_SUPABASE_CONFIGURED }}
              name: Supabase deployment skipped
              run: echo ':warning:Supabase deployment skipped' >> $GITHUB_STEP_SUMMARY

            - name: 📡 Deploy GitHub pages
              run: npx gh-pages --remote production -d dist -b ${{ vars.DEPLOY_BRANCH || 'gh-pages' }}
              env:
                  GITHUB_TOKEN: ${{ secrets.DEPLOY_TOKEN || secrets.GITHUB_TOKEN }}
