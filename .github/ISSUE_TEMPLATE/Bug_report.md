---
name: "\U0001F41B Bug report"
about: Something isn't working as expected. Please tell us!

---

**What you were expecting:**
<!-- Describe what the behavior would be without the bug. -->

**What happened instead:**
<!-- Describe how the bug manifests. -->

**Steps to reproduce:**
<!--  Please explain the steps required to duplicate the issue, especially if you are able to provide a sample application. -->

**Related code:**
<!-- If you are able to illustrate the bug or feature request with an example, please provide a sample application via one of the following means: -->

* A link to a GitHub repo with the minimal codebase to reproduce the issue


```
insert short code snippets here
```

**Other information:**
<!-- List any other information that is relevant to your issue. Stack traces, related issues, suggestions on how to fix, Stack Overflow links, forum links, etc. For visual or layout problems, please include images or animated gifs.-->

**Environment**

* atomic-crm version:
* Last version that did not exhibit the issue (if applicable):
* React version:
* Browser:
* Stack trace (in case of a JS error):