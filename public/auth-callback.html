<!doctype html>
<html lang="en">
    <head>
        <meta charset="utf-8" />
        <meta
            name="viewport"
            content="minimum-scale=1, initial-scale=1, width=device-width, shrink-to-fit=no"
        />
        <meta name="theme-color" content="#000000" />
        <link rel="manifest" href="./manifest.json" />
        <link rel="shortcut icon" href="./favicon.ico" />
        <title>Atomic CRM</title>
        <style>
            body {
                margin: 0;
                padding: 0;
                font-family: sans-serif;
            }

            .loader-container {
                display: flex;
                align-items: center;
                justify-content: center;
                flex-direction: column;
                position: absolute;
                top: 0;
                bottom: 0;
                left: 0;
                right: 0;
                background-color: #fafafa;
            }

            /* CSS Spinner from https://projects.lukehaas.me/css-loaders/ */

            .loader,
            .loader:before,
            .loader:after {
                border-radius: 50%;
            }

            .loader {
                color: #283593;
                font-size: 11px;
                text-indent: -99999em;
                margin: 55px auto;
                position: relative;
                width: 10em;
                height: 10em;
                box-shadow: inset 0 0 0 1em;
                -webkit-transform: translateZ(0);
                -ms-transform: translateZ(0);
                transform: translateZ(0);
            }

            .loader:before,
            .loader:after {
                position: absolute;
                content: '';
            }

            .loader:before {
                width: 5.2em;
                height: 10.2em;
                background: #fafafa;
                border-radius: 10.2em 0 0 10.2em;
                top: -0.1em;
                left: -0.1em;
                -webkit-transform-origin: 5.2em 5.1em;
                transform-origin: 5.2em 5.1em;
                -webkit-animation: load2 2s infinite ease 1.5s;
                animation: load2 2s infinite ease 1.5s;
            }

            .loader:after {
                width: 5.2em;
                height: 10.2em;
                background: #fafafa;
                border-radius: 0 10.2em 10.2em 0;
                top: -0.1em;
                left: 5.1em;
                -webkit-transform-origin: 0px 5.1em;
                transform-origin: 0px 5.1em;
                -webkit-animation: load2 2s infinite ease;
                animation: load2 2s infinite ease;
            }

            @-webkit-keyframes load2 {
                0% {
                    -webkit-transform: rotate(0deg);
                    transform: rotate(0deg);
                }
                100% {
                    -webkit-transform: rotate(360deg);
                    transform: rotate(360deg);
                }
            }

            @keyframes load2 {
                0% {
                    -webkit-transform: rotate(0deg);
                    transform: rotate(0deg);
                }
                100% {
                    -webkit-transform: rotate(360deg);
                    transform: rotate(360deg);
                }
            }
        </style>
    </head>

    <body>
        <noscript> You need to enable JavaScript to run this app. </noscript>
        <div id="root">
            <div class="loader-container">
                <div class="loader">Loading...</div>
            </div>
        </div>
    </body>
    <script>
        const getUrlParams = () => {
            const urlSearchParams = new URLSearchParams(
                window.location.hash.substring(1)
            );
            const access_token = urlSearchParams.get('access_token');
            const refresh_token = urlSearchParams.get('refresh_token');
            const type = urlSearchParams.get('type');

            return { access_token, refresh_token, type };
        };

        function interceptAuthCallback() {
                const hash = window.location.hash;
                const {access_token, refresh_token, type} = getUrlParams();
                window.location.href = `./#/auth-callback?access_token=${access_token}&refresh_token=${refresh_token}&type=${type}`;
        }
    
        // Call the function to intercept the auth callback
        interceptAuthCallback();
    </script>
</html>
