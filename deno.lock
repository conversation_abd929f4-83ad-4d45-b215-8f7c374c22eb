{"version": "3", "packages": {"specifiers": {"jsr:@supabase/functions-js": "jsr:@supabase/functions-js@2.4.3", "jsr:@supabase/supabase-js@2": "jsr:@supabase/supabase-js@2.45.0", "npm:@mui/material@^5.15.6": "npm:@mui/material@5.16.6_react@18.3.1_react-dom@18.3.1__react@18.3.1_@emotion+react@11.13.0__react@18.3.1_@emotion+styled@11.13.0__@emotion+react@11.13.0___react@18.3.1__react@18.3.1", "npm:@supabase/auth-js@2.64.4": "npm:@supabase/auth-js@2.64.4", "npm:@supabase/functions-js@2.4.1": "npm:@supabase/functions-js@2.4.1", "npm:@supabase/node-fetch@2.6.15": "npm:@supabase/node-fetch@2.6.15", "npm:@supabase/postgrest-js@1.15.8": "npm:@supabase/postgrest-js@1.15.8", "npm:@supabase/realtime-js@2.10.2": "npm:@supabase/realtime-js@2.10.2", "npm:@supabase/storage-js@2.6.0": "npm:@supabase/storage-js@2.6.0", "npm:@types/node": "npm:@types/node@18.16.19", "npm:openai@^4.52.5": "npm:openai@4.54.0", "npm:ra-supabase@^3.0.0": "npm:ra-supabase@3.0.0_@mui+material@5.16.6__react@18.3.1__react-dom@18.3.1___react@18.3.1__@emotion+react@11.13.0___react@18.3.1__@emotion+styled@11.13.0___@emotion+react@11.13.0____react@18.3.1___react@18.3.1_react@18.3.1", "npm:react-admin@^5.1.0": "npm:react-admin@5.1.1_react@18.3.1_react-dom@18.3.1__react@18.3.1_@emotion+react@11.13.0__react@18.3.1_@mui+material@5.16.6__@emotion+react@11.13.0___react@18.3.1__@emotion+styled@11.13.0___@emotion+react@11.13.0____react@18.3.1___react@18.3.1__react@18.3.1__react-dom@18.3.1___react@18.3.1_@emotion+styled@11.13.0__@emotion+react@11.13.0___react@18.3.1__react@18.3.1_react-hook-form@7.52.2__react@18.3.1_react-router@6.26.0__react@18.3.1_react-router-dom@6.26.0__react@18.3.1__react-dom@18.3.1___react@18.3.1_@mui+icons-material@5.16.6__@mui+material@5.16.6___@emotion+react@11.13.0____react@18.3.1___@emotion+styled@11.13.0____@emotion+react@11.13.0_____react@18.3.1____react@18.3.1___react@18.3.1___react-dom@18.3.1____react@18.3.1__react@18.3.1__@emotion+react@11.13.0___react@18.3.1__@emotion+styled@11.13.0___@emotion+react@11.13.0____react@18.3.1___react@18.3.1__react-dom@18.3.1___react@18.3.1_ra-core@5.1.1__react@18.3.1__react-dom@18.3.1___react@18.3.1__react-hook-form@7.52.2___react@18.3.1__react-router@6.26.0___react@18.3.1__react-router-dom@6.26.0___react@18.3.1___react-dom@18.3.1____react@18.3.1", "npm:react@^18.2.0": "npm:react@18.3.1"}, "jsr": {"@supabase/functions-js@2.4.3": {"integrity": "010834c9e7d2996b4be8e4004a15ed8f42b326490e4b354683f9c191353b76cc", "dependencies": ["npm:openai@^4.52.5"]}, "@supabase/supabase-js@2.45.0": {"integrity": "f6a0f1cbd18e36a52b5185c56f7afd6014124554fff2885fb8de0dd3cd3904b3", "dependencies": ["npm:@supabase/auth-js@2.64.4", "npm:@supabase/functions-js@2.4.1", "npm:@supabase/node-fetch@2.6.15", "npm:@supabase/postgrest-js@1.15.8", "npm:@supabase/realtime-js@2.10.2", "npm:@supabase/storage-js@2.6.0"]}}, "npm": {"@babel/code-frame@7.24.7": {"integrity": "sha512-BcYH1CVJBO9tvyIZ2jVeXgSIMvGZ2FDRvDdOIVQyuklNKSsx+eppDEBq/g47Ayw+RqNFE+URvOShmf+f/qwAlA==", "dependencies": {"@babel/highlight": "@babel/highlight@7.24.7", "picocolors": "picocolors@1.0.1"}}, "@babel/generator@7.25.0": {"integrity": "sha512-3LEEcj3PVW8pW2R1SR1M89g/qrYk/m/mB/tLqn7dn4sbBUQyTqnlod+II2U4dqiGtUmkcnAmkMDralTFZttRiw==", "dependencies": {"@babel/types": "@babel/types@7.25.2", "@jridgewell/gen-mapping": "@jridgewell/gen-mapping@0.3.5", "@jridgewell/trace-mapping": "@jridgewell/trace-mapping@0.3.25", "jsesc": "jsesc@2.5.2"}}, "@babel/helper-module-imports@7.24.7": {"integrity": "sha512-8AyH3C+74cgCVVXow/myrynrAGv+nTVg5vKu2nZph9x7RcRwzmh0VFallJuFTZ9mx6u4eSdXZfcOzSqTUm0HCA==", "dependencies": {"@babel/traverse": "@babel/traverse@7.25.3", "@babel/types": "@babel/types@7.25.2"}}, "@babel/helper-string-parser@7.24.8": {"integrity": "sha512-pO9KhhRcuUyGnJWwyEgnRJTSIZHiT+vMD0kPeD+so0l7mxkMT19g3pjY9GTnHySck/hDzq+dtW/4VgnMkippsQ==", "dependencies": {}}, "@babel/helper-validator-identifier@7.24.7": {"integrity": "sha512-rR+PBcQ1SMQDDyF6X0wxtG8QyLCgUB0eRAGguqRLfkCA87l7yAP7ehq8SNj96OOGTO8OBV70KhuFYcIkHXOg0w==", "dependencies": {}}, "@babel/highlight@7.24.7": {"integrity": "sha512-EStJpq4OuY8xYfhGVXngigBJRWxftKX9ksiGDnmlY3o7B/V7KIAc9X4oiK87uPJSc/vs5L869bem5fhZa8caZw==", "dependencies": {"@babel/helper-validator-identifier": "@babel/helper-validator-identifier@7.24.7", "chalk": "chalk@2.4.2", "js-tokens": "js-tokens@4.0.0", "picocolors": "picocolors@1.0.1"}}, "@babel/parser@7.25.3": {"integrity": "sha512-iLTJKDbJ4hMvFPgQwwsVoxtHyWpKKPBrxkANrSYewDPaPpT5py5yeVkgPIJ7XYXhndxJpaA3PyALSXQ7u8e/Dw==", "dependencies": {"@babel/types": "@babel/types@7.25.2"}}, "@babel/runtime@7.25.0": {"integrity": "sha512-7dRy4DwXwtzBrPbZflqxnvfxLF8kdZXPkhymtDeFoFqE6ldzjQFgYTtYIFARcLEYDrqfBfYcZt1WqFxRoyC9Rw==", "dependencies": {"regenerator-runtime": "regenerator-runtime@0.14.1"}}, "@babel/template@7.25.0": {"integrity": "sha512-aOOgh1/5XzKvg1jvVz7AVrx2piJ2XBi227DHmbY6y+bM9H2FlN+IfecYu4Xl0cNiiVejlsCri89LUsbj8vJD9Q==", "dependencies": {"@babel/code-frame": "@babel/code-frame@7.24.7", "@babel/parser": "@babel/parser@7.25.3", "@babel/types": "@babel/types@7.25.2"}}, "@babel/traverse@7.25.3": {"integrity": "sha512-HefgyP1x754oGCsKmV5reSmtV7IXj/kpaE1XYY+D9G5PvKKoFfSbiS4M77MdjuwlZKDIKFCffq9rPU+H/s3ZdQ==", "dependencies": {"@babel/code-frame": "@babel/code-frame@7.24.7", "@babel/generator": "@babel/generator@7.25.0", "@babel/parser": "@babel/parser@7.25.3", "@babel/template": "@babel/template@7.25.0", "@babel/types": "@babel/types@7.25.2", "debug": "debug@4.3.6", "globals": "globals@11.12.0"}}, "@babel/types@7.25.2": {"integrity": "sha512-YTnYtra7W9e6/oAZEHj0bJehPRUlLH9/fbpT5LfB0NhQXyALCRkRs3zH9v07IYhkgpqX6Z78FnuccZr/l4Fs4Q==", "dependencies": {"@babel/helper-string-parser": "@babel/helper-string-parser@7.24.8", "@babel/helper-validator-identifier": "@babel/helper-validator-identifier@7.24.7", "to-fast-properties": "to-fast-properties@2.0.0"}}, "@emotion/babel-plugin@11.12.0": {"integrity": "sha512-y2WQb+oP8Jqvvclh8Q55gLUyb7UFvgv7eJfsj7td5TToBrIUtPay2kMrZi4xjq9qw2vD0ZR5fSho0yqoFgX7Rw==", "dependencies": {"@babel/helper-module-imports": "@babel/helper-module-imports@7.24.7", "@babel/runtime": "@babel/runtime@7.25.0", "@emotion/hash": "@emotion/hash@0.9.2", "@emotion/memoize": "@emotion/memoize@0.9.0", "@emotion/serialize": "@emotion/serialize@1.3.0", "babel-plugin-macros": "babel-plugin-macros@3.1.0", "convert-source-map": "convert-source-map@1.9.0", "escape-string-regexp": "escape-string-regexp@4.0.0", "find-root": "find-root@1.1.0", "source-map": "source-map@0.5.7", "stylis": "stylis@4.2.0"}}, "@emotion/cache@11.13.1": {"integrity": "sha512-iqouYkuEblRcXmylXIwwOodiEK5Ifl7JcX7o6V4jI3iW4mLXX3dmt5xwBtIkJiQEXFAI+pC8X0i67yiPkH9Ucw==", "dependencies": {"@emotion/memoize": "@emotion/memoize@0.9.0", "@emotion/sheet": "@emotion/sheet@1.4.0", "@emotion/utils": "@emotion/utils@1.4.0", "@emotion/weak-memoize": "@emotion/weak-memoize@0.4.0", "stylis": "stylis@4.2.0"}}, "@emotion/hash@0.9.2": {"integrity": "sha512-MyqliTZGuOm3+5ZRSaaBGP3USLw6+EGykkwZns2EPC5g8jJ4z9OrdZY9apkl3+UP9+sdz76YYkwCKP5gh8iY3g==", "dependencies": {}}, "@emotion/is-prop-valid@1.3.0": {"integrity": "sha512-SHetuSLvJDzuNbOdtPVbq6yMMMlLoW5Q94uDqJZqy50gcmAjxFkVqmzqSGEFq9gT2iMuIeKV1PXVWmvUhuZLlQ==", "dependencies": {"@emotion/memoize": "@emotion/memoize@0.9.0"}}, "@emotion/memoize@0.9.0": {"integrity": "sha512-30FAj7/EoJ5mwVPOWhAyCX+FPfMDrVecJAM+Iw9NRoSl4BBAQeqj4cApHHUXOVvIPgLVDsCFoz/hGD+5QQD1GQ==", "dependencies": {}}, "@emotion/react@11.13.0_react@18.3.1": {"integrity": "sha512-WkL+bw1REC2VNV1goQyfxjx1GYJkcc23CRQkXX+vZNLINyfI7o+uUn/rTGPt/xJ3bJHd5GcljgnxHf4wRw5VWQ==", "dependencies": {"@babel/runtime": "@babel/runtime@7.25.0", "@emotion/babel-plugin": "@emotion/babel-plugin@11.12.0", "@emotion/cache": "@emotion/cache@11.13.1", "@emotion/serialize": "@emotion/serialize@1.3.0", "@emotion/use-insertion-effect-with-fallbacks": "@emotion/use-insertion-effect-with-fallbacks@1.1.0_react@18.3.1", "@emotion/utils": "@emotion/utils@1.4.0", "@emotion/weak-memoize": "@emotion/weak-memoize@0.4.0", "hoist-non-react-statics": "hoist-non-react-statics@3.3.2", "react": "react@18.3.1"}}, "@emotion/serialize@1.3.0": {"integrity": "sha512-jACuBa9SlYajnpIVXB+XOXnfJHyckDfe6fOpORIM6yhBDlqGuExvDdZYHDQGoDf3bZXGv7tNr+LpLjJqiEQ6EA==", "dependencies": {"@emotion/hash": "@emotion/hash@0.9.2", "@emotion/memoize": "@emotion/memoize@0.9.0", "@emotion/unitless": "@emotion/unitless@0.9.0", "@emotion/utils": "@emotion/utils@1.4.0", "csstype": "csstype@3.1.3"}}, "@emotion/sheet@1.4.0": {"integrity": "sha512-fTBW9/8r2w3dXWYM4HCB1Rdp8NLibOw2+XELH5m5+AkWiL/KqYX6dc0kKYlaYyKjrQ6ds33MCdMPEwgs2z1rqg==", "dependencies": {}}, "@emotion/styled@11.13.0_@emotion+react@11.13.0__react@18.3.1_react@18.3.1": {"integrity": "sha512-tkzkY7nQhW/zC4hztlwucpT8QEZ6eUzpXDRhww/Eej4tFfO0FxQYWRyg/c5CCXa4d/f174kqeXYjuQRnhzf6dA==", "dependencies": {"@babel/runtime": "@babel/runtime@7.25.0", "@emotion/babel-plugin": "@emotion/babel-plugin@11.12.0", "@emotion/is-prop-valid": "@emotion/is-prop-valid@1.3.0", "@emotion/react": "@emotion/react@11.13.0_react@18.3.1", "@emotion/serialize": "@emotion/serialize@1.3.0", "@emotion/use-insertion-effect-with-fallbacks": "@emotion/use-insertion-effect-with-fallbacks@1.1.0_react@18.3.1", "@emotion/utils": "@emotion/utils@1.4.0", "react": "react@18.3.1"}}, "@emotion/unitless@0.9.0": {"integrity": "sha512-TP6GgNZtmtFaFcsOgExdnfxLLpRDla4Q66tnenA9CktvVSdNKDvMVuUah4QvWPIpNjrWsGg3qeGo9a43QooGZQ==", "dependencies": {}}, "@emotion/use-insertion-effect-with-fallbacks@1.1.0_react@18.3.1": {"integrity": "sha512-+wBOcIV5snwGgI2ya3u99D7/FJquOIniQT1IKyDsBmEgwvpxMNeS65Oib7OnE2d2aY+3BU4OiH+0Wchf8yk3Hw==", "dependencies": {"react": "react@18.3.1"}}, "@emotion/utils@1.4.0": {"integrity": "sha512-spEnrA1b6hDR/C68lC2M7m6ALPUHZC0lIY7jAS/B/9DuuO1ZP04eov8SMv/6fwRd8pzmsn2AuJEznRREWlQrlQ==", "dependencies": {}}, "@emotion/weak-memoize@0.4.0": {"integrity": "sha512-snKqtPW01tN0ui7yu9rGv69aJXr/a/Ywvl11sUjNtEcRc+ng/mQriFL0wLXMef74iHa/EkftbDzU9F8iFbH+zg==", "dependencies": {}}, "@jridgewell/gen-mapping@0.3.5": {"integrity": "sha512-IzL8ZoEDIBRWEzlCcRhOaCupYyN5gdIK+Q6fbFdPDg6HqX6jpkItn7DFIpW9LQzXG6Df9sA7+OKnq0qlz/GaQg==", "dependencies": {"@jridgewell/set-array": "@jridgewell/set-array@1.2.1", "@jridgewell/sourcemap-codec": "@jridgewell/sourcemap-codec@1.5.0", "@jridgewell/trace-mapping": "@jridgewell/trace-mapping@0.3.25"}}, "@jridgewell/resolve-uri@3.1.2": {"integrity": "sha512-bRISgCIjP20/tbWSPWMEi54QVPRZExkuD9lJL+UIxUKtwVJA8wW1Trb1jMs1RFXo1CBTNZ/5hpC9QvmKWdopKw==", "dependencies": {}}, "@jridgewell/set-array@1.2.1": {"integrity": "sha512-R8gLRTZeyp03ymzP/6Lil/28tGeGEzhx1q2k703KGWRAI1VdvPIXdG70VJc2pAMw3NA6JKL5hhFu1sJX0Mnn/A==", "dependencies": {}}, "@jridgewell/sourcemap-codec@1.5.0": {"integrity": "sha512-gv3ZRaISU3fjPAgNsriBRqGWQL6quFx04YMPW/zD8XMLsU32mhCCbfbO6KZFLjvYpCZ8zyDEgqsgf+PwPaM7GQ==", "dependencies": {}}, "@jridgewell/trace-mapping@0.3.25": {"integrity": "sha512-vNk6aEwybGtawWmy/PzwnGDOjCkLWSD2wqvjGGAgOAwCGWySYXfYoxt00IJkTF+8Lb57DwOb3Aa0o9CApepiYQ==", "dependencies": {"@jridgewell/resolve-uri": "@jridgewell/resolve-uri@3.1.2", "@jridgewell/sourcemap-codec": "@jridgewell/sourcemap-codec@1.5.0"}}, "@mui/core-downloads-tracker@5.16.6": {"integrity": "sha512-kytg6LheUG42V8H/o/Ptz3olSO5kUXW9zF0ox18VnblX6bO2yif1FPItgc3ey1t5ansb1+gbe7SatntqusQupg==", "dependencies": {}}, "@mui/icons-material@5.16.6_@mui+material@5.16.6__@emotion+react@11.13.0___react@18.3.1__@emotion+styled@11.13.0___@emotion+react@11.13.0____react@18.3.1___react@18.3.1__react@18.3.1__react-dom@18.3.1___react@18.3.1_react@18.3.1_@emotion+react@11.13.0__react@18.3.1_@emotion+styled@11.13.0__@emotion+react@11.13.0___react@18.3.1__react@18.3.1_react-dom@18.3.1__react@18.3.1": {"integrity": "sha512-ceNGjoXheH9wbIFa1JHmSc9QVjJUvh18KvHrR4/FkJCSi9HXJ+9ee1kUhCOEFfuxNF8UB6WWVrIUOUgRd70t0A==", "dependencies": {"@babel/runtime": "@babel/runtime@7.25.0", "@mui/material": "@mui/material@5.16.6_@emotion+react@11.13.0__react@18.3.1_@emotion+styled@11.13.0__@emotion+react@11.13.0___react@18.3.1__react@18.3.1_react@18.3.1_react-dom@18.3.1__react@18.3.1", "react": "react@18.3.1"}}, "@mui/icons-material@5.16.6_@mui+material@5.16.6__react@18.3.1__react-dom@18.3.1___react@18.3.1__@emotion+react@11.13.0___react@18.3.1__@emotion+styled@11.13.0___@emotion+react@11.13.0____react@18.3.1___react@18.3.1_react@18.3.1_react-dom@18.3.1__react@18.3.1": {"integrity": "sha512-ceNGjoXheH9wbIFa1JHmSc9QVjJUvh18KvHrR4/FkJCSi9HXJ+9ee1kUhCOEFfuxNF8UB6WWVrIUOUgRd70t0A==", "dependencies": {"@babel/runtime": "@babel/runtime@7.25.0", "@mui/material": "@mui/material@5.16.6_react@18.3.1_react-dom@18.3.1__react@18.3.1_@emotion+react@11.13.0__react@18.3.1_@emotion+styled@11.13.0__@emotion+react@11.13.0___react@18.3.1__react@18.3.1", "react": "react@18.3.1"}}, "@mui/material@5.16.6_@emotion+react@11.13.0__react@18.3.1_@emotion+styled@11.13.0__@emotion+react@11.13.0___react@18.3.1__react@18.3.1_react@18.3.1_react-dom@18.3.1__react@18.3.1": {"integrity": "sha512-0LUIKBOIjiFfzzFNxXZBRAyr9UQfmTAFzbt6ziOU2FDXhorNN2o3N9/32mNJbCA8zJo2FqFU6d3dtoqUDyIEfA==", "dependencies": {"@babel/runtime": "@babel/runtime@7.25.0", "@emotion/react": "@emotion/react@11.13.0_react@18.3.1", "@emotion/styled": "@emotion/styled@11.13.0_@emotion+react@11.13.0__react@18.3.1_react@18.3.1", "@mui/core-downloads-tracker": "@mui/core-downloads-tracker@5.16.6", "@mui/system": "@mui/system@5.16.6_@emotion+react@11.13.0__react@18.3.1_@emotion+styled@11.13.0__@emotion+react@11.13.0___react@18.3.1__react@18.3.1_react@18.3.1", "@mui/types": "@mui/types@7.2.15", "@mui/utils": "@mui/utils@5.16.6_react@18.3.1", "@popperjs/core": "@popperjs/core@2.11.8", "@types/react-transition-group": "@types/react-transition-group@4.4.10", "clsx": "clsx@2.1.1", "csstype": "csstype@3.1.3", "prop-types": "prop-types@15.8.1", "react": "react@18.3.1", "react-dom": "react-dom@18.3.1_react@18.3.1", "react-is": "react-is@18.3.1", "react-transition-group": "react-transition-group@4.4.5_react@18.3.1_react-dom@18.3.1__react@18.3.1"}}, "@mui/material@5.16.6_react@18.3.1_react-dom@18.3.1__react@18.3.1_@emotion+react@11.13.0__react@18.3.1_@emotion+styled@11.13.0__@emotion+react@11.13.0___react@18.3.1__react@18.3.1": {"integrity": "sha512-0LUIKBOIjiFfzzFNxXZBRAyr9UQfmTAFzbt6ziOU2FDXhorNN2o3N9/32mNJbCA8zJo2FqFU6d3dtoqUDyIEfA==", "dependencies": {"@babel/runtime": "@babel/runtime@7.25.0", "@emotion/react": "@emotion/react@11.13.0_react@18.3.1", "@emotion/styled": "@emotion/styled@11.13.0_@emotion+react@11.13.0__react@18.3.1_react@18.3.1", "@mui/core-downloads-tracker": "@mui/core-downloads-tracker@5.16.6", "@mui/system": "@mui/system@5.16.6_react@18.3.1_@emotion+react@11.13.0__react@18.3.1_@emotion+styled@11.13.0__@emotion+react@11.13.0___react@18.3.1__react@18.3.1", "@mui/types": "@mui/types@7.2.15", "@mui/utils": "@mui/utils@5.16.6_react@18.3.1", "@popperjs/core": "@popperjs/core@2.11.8", "@types/react-transition-group": "@types/react-transition-group@4.4.10", "clsx": "clsx@2.1.1", "csstype": "csstype@3.1.3", "prop-types": "prop-types@15.8.1", "react": "react@18.3.1", "react-dom": "react-dom@18.3.1_react@18.3.1", "react-is": "react-is@18.3.1", "react-transition-group": "react-transition-group@4.4.5_react@18.3.1_react-dom@18.3.1__react@18.3.1"}}, "@mui/private-theming@5.16.6_react@18.3.1": {"integrity": "sha512-rAk+Rh8Clg7Cd7shZhyt2HGTTE5wYKNSJ5sspf28Fqm/PZ69Er9o6KX25g03/FG2dfpg5GCwZh/xOojiTfm3hw==", "dependencies": {"@babel/runtime": "@babel/runtime@7.25.0", "@mui/utils": "@mui/utils@5.16.6_react@18.3.1", "prop-types": "prop-types@15.8.1", "react": "react@18.3.1"}}, "@mui/styled-engine@5.16.6_@emotion+react@11.13.0__react@18.3.1_@emotion+styled@11.13.0__@emotion+react@11.13.0___react@18.3.1__react@18.3.1_react@18.3.1": {"integrity": "sha512-zaThmS67ZmtHSWToTiHslbI8jwrmITcN93LQaR2lKArbvS7Z3iLkwRoiikNWutx9MBs8Q6okKvbZq1RQYB3v7g==", "dependencies": {"@babel/runtime": "@babel/runtime@7.25.0", "@emotion/cache": "@emotion/cache@11.13.1", "@emotion/react": "@emotion/react@11.13.0_react@18.3.1", "@emotion/styled": "@emotion/styled@11.13.0_@emotion+react@11.13.0__react@18.3.1_react@18.3.1", "csstype": "csstype@3.1.3", "prop-types": "prop-types@15.8.1", "react": "react@18.3.1"}}, "@mui/system@5.16.6_@emotion+react@11.13.0__react@18.3.1_@emotion+styled@11.13.0__@emotion+react@11.13.0___react@18.3.1__react@18.3.1_react@18.3.1": {"integrity": "sha512-5xgyJjBIMPw8HIaZpfbGAaFYPwImQn7Nyh+wwKWhvkoIeDosQ1ZMVrbTclefi7G8hNmqhip04duYwYpbBFnBgw==", "dependencies": {"@babel/runtime": "@babel/runtime@7.25.0", "@emotion/react": "@emotion/react@11.13.0_react@18.3.1", "@emotion/styled": "@emotion/styled@11.13.0_@emotion+react@11.13.0__react@18.3.1_react@18.3.1", "@mui/private-theming": "@mui/private-theming@5.16.6_react@18.3.1", "@mui/styled-engine": "@mui/styled-engine@5.16.6_@emotion+react@11.13.0__react@18.3.1_@emotion+styled@11.13.0__@emotion+react@11.13.0___react@18.3.1__react@18.3.1_react@18.3.1", "@mui/types": "@mui/types@7.2.15", "@mui/utils": "@mui/utils@5.16.6_react@18.3.1", "clsx": "clsx@2.1.1", "csstype": "csstype@3.1.3", "prop-types": "prop-types@15.8.1", "react": "react@18.3.1"}}, "@mui/system@5.16.6_react@18.3.1_@emotion+react@11.13.0__react@18.3.1_@emotion+styled@11.13.0__@emotion+react@11.13.0___react@18.3.1__react@18.3.1": {"integrity": "sha512-5xgyJjBIMPw8HIaZpfbGAaFYPwImQn7Nyh+wwKWhvkoIeDosQ1ZMVrbTclefi7G8hNmqhip04duYwYpbBFnBgw==", "dependencies": {"@babel/runtime": "@babel/runtime@7.25.0", "@emotion/react": "@emotion/react@11.13.0_react@18.3.1", "@emotion/styled": "@emotion/styled@11.13.0_@emotion+react@11.13.0__react@18.3.1_react@18.3.1", "@mui/private-theming": "@mui/private-theming@5.16.6_react@18.3.1", "@mui/styled-engine": "@mui/styled-engine@5.16.6_@emotion+react@11.13.0__react@18.3.1_@emotion+styled@11.13.0__@emotion+react@11.13.0___react@18.3.1__react@18.3.1_react@18.3.1", "@mui/types": "@mui/types@7.2.15", "@mui/utils": "@mui/utils@5.16.6_react@18.3.1", "clsx": "clsx@2.1.1", "csstype": "csstype@3.1.3", "prop-types": "prop-types@15.8.1", "react": "react@18.3.1"}}, "@mui/types@7.2.15": {"integrity": "sha512-nbo7yPhtKJkdf9kcVOF8JZHPZTmqXjJ/tI0bdWgHg5tp9AnIN4Y7f7wm9T+0SyGYJk76+GYZ8Q5XaTYAsUHN0Q==", "dependencies": {}}, "@mui/utils@5.16.6_react@18.3.1": {"integrity": "sha512-tWiQqlhxAt3KENNiSRL+DIn9H5xNVK6Jjf70x3PnfQPz1MPBdh7yyIcAyVBT9xiw7hP3SomRhPR7hzBMBCjqEA==", "dependencies": {"@babel/runtime": "@babel/runtime@7.25.0", "@mui/types": "@mui/types@7.2.15", "@types/prop-types": "@types/prop-types@15.7.12", "clsx": "clsx@2.1.1", "prop-types": "prop-types@15.8.1", "react": "react@18.3.1", "react-is": "react-is@18.3.1"}}, "@popperjs/core@2.11.8": {"integrity": "sha512-P1st0aksCrn9sGZhp8GMYwBnQsbvAWsZAX44oXNNvLHGqAOcoVxmjZiohstwQ7SqKnbR47akdNi+uleWD8+g6A==", "dependencies": {}}, "@raphiniert/ra-data-postgrest@2.3.0_ra-core@5.1.1__react@18.3.1__react-dom@18.3.1___react@18.3.1__react-hook-form@7.52.2___react@18.3.1__react-router@6.26.0___react@18.3.1__react-router-dom@6.26.0___react@18.3.1___react-dom@18.3.1____react@18.3.1_react@18.3.1": {"integrity": "sha512-AV2j+hbNrFMDXkjFMKzNB3hA+2Vd+3UiUVqsuBInHzVN7gHru+eRNcF1LwF5mjpYRBOHGW7DqNN2mwSNCfpTRA==", "dependencies": {"lodash.isequal": "lodash.isequal@4.5.0", "qs": "qs@6.13.0", "ra-core": "ra-core@5.1.1_react@18.3.1_react-dom@18.3.1__react@18.3.1_react-hook-form@7.52.2__react@18.3.1_react-router@6.26.0__react@18.3.1_react-router-dom@6.26.0__react@18.3.1__react-dom@18.3.1___react@18.3.1"}}, "@raphiniert/ra-data-postgrest@2.3.0_ra-core@5.1.1__react@18.3.1__react-dom@18.3.1___react@18.3.1__react-hook-form@7.52.2___react@18.3.1__react-router@6.26.0___react@18.3.1__react-router-dom@6.26.0___react@18.3.1___react-dom@18.3.1____react@18.3.1_react@18.3.1_react-dom@18.3.1__react@18.3.1_react-router@6.26.0__react@18.3.1": {"integrity": "sha512-AV2j+hbNrFMDXkjFMKzNB3hA+2Vd+3UiUVqsuBInHzVN7gHru+eRNcF1LwF5mjpYRBOHGW7DqNN2mwSNCfpTRA==", "dependencies": {"lodash.isequal": "lodash.isequal@4.5.0", "qs": "qs@6.13.0", "ra-core": "ra-core@5.1.1_react@18.3.1_react-dom@18.3.1__react@18.3.1_react-hook-form@7.52.2__react@18.3.1_react-router@6.26.0__react@18.3.1_react-router-dom@6.26.0__react@18.3.1__react-dom@18.3.1___react@18.3.1"}}, "@remix-run/router@1.19.0": {"integrity": "sha512-zDICCLKEwbVYTS6TjYaWtHXxkdoUvD/QXvyVZjGCsWz5vyH7aFeONlPffPdW+Y/t6KT0MgXb2Mfjun9YpWN1dA==", "dependencies": {}}, "@supabase/auth-js@2.64.4": {"integrity": "sha512-9ITagy4WP4FLl+mke1rchapOH0RQpf++DI+WSG2sO1OFOZ0rW3cwAM0nCrMOxu+Zw4vJ4zObc08uvQrXx590Tg==", "dependencies": {"@supabase/node-fetch": "@supabase/node-fetch@2.6.15"}}, "@supabase/functions-js@2.4.1": {"integrity": "sha512-8sZ2ibwHlf+WkHDUZJUXqqmPvWQ3UHN0W30behOJngVh/qHHekhJLCFbh0AjkE9/FqqXtf9eoVvmYgfCLk5tNA==", "dependencies": {"@supabase/node-fetch": "@supabase/node-fetch@2.6.15"}}, "@supabase/node-fetch@2.6.15": {"integrity": "sha512-1ibVeYUacxWYi9i0cf5efil6adJ9WRyZBLivgjs+AUpewx1F3xPi7gLgaASI2SmIQxPoCEjAsLAzKPgMJVgOUQ==", "dependencies": {"whatwg-url": "whatwg-url@5.0.0"}}, "@supabase/postgrest-js@1.15.8": {"integrity": "sha512-YunjXpoQjQ0a0/7vGAvGZA2dlMABXFdVI/8TuVKtlePxyT71sl6ERl6ay1fmIeZcqxiuFQuZw/LXUuStUG9bbg==", "dependencies": {"@supabase/node-fetch": "@supabase/node-fetch@2.6.15"}}, "@supabase/realtime-js@2.10.2": {"integrity": "sha512-qyCQaNg90HmJstsvr2aJNxK2zgoKh9ZZA8oqb7UT2LCh3mj9zpa3Iwu167AuyNxsxrUE8eEJ2yH6wLCij4EApA==", "dependencies": {"@supabase/node-fetch": "@supabase/node-fetch@2.6.15", "@types/phoenix": "@types/phoenix@1.6.5", "@types/ws": "@types/ws@8.5.12", "ws": "ws@8.18.0"}}, "@supabase/storage-js@2.6.0": {"integrity": "sha512-REAxr7myf+3utMkI2oOmZ6sdplMZZ71/2NEIEMBZHL9Fkmm3/JnaOZVSRqvG4LStYj2v5WhCruCzuMn6oD/Drw==", "dependencies": {"@supabase/node-fetch": "@supabase/node-fetch@2.6.15"}}, "@supabase/supabase-js@2.45.1": {"integrity": "sha512-/PVe3lXmalazD8BGMIoI7+ttvT1mLXy13lNcoAPtjP1TDDY83g8csZbVR6l+0/RZtvJxl3LGXfTJT4bjWgC5Nw==", "dependencies": {"@supabase/auth-js": "@supabase/auth-js@2.64.4", "@supabase/functions-js": "@supabase/functions-js@2.4.1", "@supabase/node-fetch": "@supabase/node-fetch@2.6.15", "@supabase/postgrest-js": "@supabase/postgrest-js@1.15.8", "@supabase/realtime-js": "@supabase/realtime-js@2.10.2", "@supabase/storage-js": "@supabase/storage-js@2.6.0"}}, "@tanstack/query-core@5.51.21": {"integrity": "sha512-POQxm42IUp6n89kKWF4IZi18v3fxQWFRolvBA6phNVmA8psdfB1MvDnGacCJdS+EOX12w/CyHM62z//rHmYmvw==", "dependencies": {}}, "@tanstack/react-query@5.51.21_react@18.3.1": {"integrity": "sha512-Q/V81x3sAYgCsxjwOkfLXfrmoG+FmDhLeHH5okC/Bp8Aaw2c33lbEo/mMcMnkxUPVtB2FLpzHT0tq3c+OlZEbw==", "dependencies": {"@tanstack/query-core": "@tanstack/query-core@5.51.21", "react": "react@18.3.1"}}, "@types/node-fetch@2.6.11": {"integrity": "sha512-24xFj9R5+rfQJLRyM56qh+wnVSYhyXC2tkoBndtY0U+vubqNsYXGjufB2nn8Q6gt0LrARwL6UBtMCSVCwl4B1g==", "dependencies": {"@types/node": "@types/node@18.16.19", "form-data": "form-data@4.0.0"}}, "@types/node@18.16.19": {"integrity": "sha512-IXl7o+R9iti9eBW4Wg2hx1xQDig183jj7YLn8F7udNceyfkbn1ZxmzZXuak20gR40D7pIkIY1kYGx5VIGbaHKA==", "dependencies": {}}, "@types/node@18.19.43": {"integrity": "sha512-Mw/YlgXnyJdEwLoFv2dpuJaDFriX+Pc+0qOBJ57jC1H6cDxIj2xc5yUrdtArDVG0m+KV6622a4p2tenEqB3C/g==", "dependencies": {"undici-types": "undici-types@5.26.5"}}, "@types/parse-json@4.0.2": {"integrity": "sha512-dISoDXWWQwUquiKsyZ4Ng+HX2KsPL7LyHKHQwgGFEA3IaKac4Obd+h2a/a6waisAoepJlBcx9paWqjA8/HVjCw==", "dependencies": {}}, "@types/phoenix@1.6.5": {"integrity": "sha512-xegpDuR+z0UqG9fwHqNoy3rI7JDlvaPh2TY47Fl80oq6g+hXT+c/LEuE43X48clZ6lOfANl5WrPur9fYO1RJ/w==", "dependencies": {}}, "@types/prop-types@15.7.12": {"integrity": "sha512-5zvhXYtRNRluoE/jAp4GVsSduVUzNWKkOZrCDBWYtE7biZywwdC2AcEzg+cSMLFRfVgeAFqpfNabiPjxFddV1Q==", "dependencies": {}}, "@types/react-transition-group@4.4.10": {"integrity": "sha512-hT/+s0VQs2ojCX823m60m5f0sL5idt9SO6Tj6Dg+rdphGPIeJbJ6CxvBYkgkGKrYeDjvIpKTR38UzmtHJOGW3Q==", "dependencies": {"@types/react": "@types/react@18.3.3"}}, "@types/react@18.3.3": {"integrity": "sha512-hti/R0pS0q1/xx+TsI73XIqk26eBsISZ2R0wUijXIngRK9R/e7Xw/cXVxQK7R5JjW+SV4zGcn5hXjudkN/pLIw==", "dependencies": {"@types/prop-types": "@types/prop-types@15.7.12", "csstype": "csstype@3.1.3"}}, "@types/ws@8.5.12": {"integrity": "sha512-3tPRkv1EtkDpzlgyKyI8pGsGZAGPEaXeu0DOj5DI25Ja91bdAYddYHbADRYVrZMRbfW+1l5YwXVDKohDJNQxkQ==", "dependencies": {"@types/node": "@types/node@18.16.19"}}, "abort-controller@3.0.0": {"integrity": "sha512-h8lQ8tacZYnR3vNQTgibj+tODHI5/+l06Au2Pcriv/Gmet0eaj4TwWH41sO9wnHDiQsEj19q0drzdWdeAHtweg==", "dependencies": {"event-target-shim": "event-target-shim@5.0.1"}}, "agentkeepalive@4.5.0": {"integrity": "sha512-5GG/5IbQQpC9FpkRGsSvZI5QYeSCzlJHdpBQntCsuTOxhKD8lqKhrleg2Yi7yvMIf82Ycmmqln9U8V9qwEiJew==", "dependencies": {"humanize-ms": "humanize-ms@1.2.1"}}, "ansi-styles@3.2.1": {"integrity": "sha512-VT0ZI6kZRdTh8YyJw3SMbYm/u+NqfsAxEpWO0Pf9sq8/e94WxxOpPKx9FR1FlyCtOVDNOQ+8ntlqFxiRc+r5qA==", "dependencies": {"color-convert": "color-convert@1.9.3"}}, "asynckit@0.4.0": {"integrity": "sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q==", "dependencies": {}}, "attr-accept@2.2.2": {"integrity": "sha512-7prDjvt9HmqiZ0cl5CRjtS84sEyhsHP2coDkaZKRKVfCDo9s7iw7ChVmar78Gu9pC4SoR/28wFu/G5JJhTnqEg==", "dependencies": {}}, "autosuggest-highlight@3.3.4": {"integrity": "sha512-j6RETBD2xYnrVcoV1S5R4t3WxOlWZKyDQjkwnggDPSjF5L4jV98ZltBpvPvbkM1HtoSe5o+bNrTHyjPbieGeYA==", "dependencies": {"remove-accents": "remove-accents@0.4.4"}}, "babel-plugin-macros@3.1.0": {"integrity": "sha512-Cg7TFGpIr01vOQNODXOOaGz2NpCU5gl8x1qJFbb6hbZxR7XrcE2vtbAsTAbJ7/xwJtUuJEw8K8Zr/AE0LHlesg==", "dependencies": {"@babel/runtime": "@babel/runtime@7.25.0", "cosmiconfig": "cosmiconfig@7.1.0", "resolve": "resolve@1.22.8"}}, "call-bind@1.0.7": {"integrity": "sha512-GHTSNSYICQ7scH7sZ+M2rFopRoLh8t2bLSW6BbgrtLsahOIB5iyAVJf9GjWK3cYTDaMj4XdBpM1cA6pIS0Kv2w==", "dependencies": {"es-define-property": "es-define-property@1.0.0", "es-errors": "es-errors@1.3.0", "function-bind": "function-bind@1.1.2", "get-intrinsic": "get-intrinsic@1.2.4", "set-function-length": "set-function-length@1.2.2"}}, "callsites@3.1.0": {"integrity": "sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ==", "dependencies": {}}, "chalk@2.4.2": {"integrity": "sha512-Mti+f9lpJNcwF4tWV8/OrTTtF1gZi+f8FqlyAdouralcFWFQWF2+NgCHShjkCb+IFBLq9buZwE1xckQU4peSuQ==", "dependencies": {"ansi-styles": "ansi-styles@3.2.1", "escape-string-regexp": "escape-string-regexp@1.0.5", "supports-color": "supports-color@5.5.0"}}, "clsx@2.1.1": {"integrity": "sha512-eYm0QWBtUrBWZWG0d386OGAw16Z995PiOVo2B7bjWSbHedGl5e0ZWaq65kOGgUSNesEIDkB9ISbTg/JK9dhCZA==", "dependencies": {}}, "color-convert@1.9.3": {"integrity": "sha512-QfAUtd+vFdAtFQcC8CCyYt1fYWxSqAiK2cSD6zDB8N3cpsEBAvRxp9zOGg6G/SHHJYAT88/az/IuDGALsNVbGg==", "dependencies": {"color-name": "color-name@1.1.3"}}, "color-name@1.1.3": {"integrity": "sha512-72fSenhMw2HZMTVHeCA9KCmpEIbzWiQsjN+BHcBbS9vr1mtt+vJjPdksIBNUmKAW8TFUDPJK5SUU3QhE9NEXDw==", "dependencies": {}}, "combined-stream@1.0.8": {"integrity": "sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg==", "dependencies": {"delayed-stream": "delayed-stream@1.0.0"}}, "convert-source-map@1.9.0": {"integrity": "sha512-ASFBup0Mz1uyiIjANan1jzLQami9z1PoYSZCiiYW2FczPbenXc45FZdBZLzOT+r6+iciuEModtmCti+hjaAk0A==", "dependencies": {}}, "cosmiconfig@7.1.0": {"integrity": "sha512-AdmX6xUzdNASswsFtmwSt7Vj8po9IuqXm0UXz7QKPuEUmPB4XyjGfaAr2PSuELMwkRMVH1EpIkX5bTZGRB3eCA==", "dependencies": {"@types/parse-json": "@types/parse-json@4.0.2", "import-fresh": "import-fresh@3.3.0", "parse-json": "parse-json@5.2.0", "path-type": "path-type@4.0.0", "yaml": "yaml@1.10.2"}}, "css-mediaquery@0.1.2": {"integrity": "sha512-COtn4EROW5dBGlE/4PiKnh6rZpAPxDeFLaEEwt4i10jpDMFt2EhQGS79QmmrO+iKCHv0PU/HrOWEhijFd1x99Q==", "dependencies": {}}, "csstype@3.1.3": {"integrity": "sha512-M1uQkMl8rQK/szD0LNhtqxIPLpimGm8sOBwU7lLnCpSbTyY3yeU1Vc7l4KT5zT4s/yOxHH5O7tIuuLOCnLADRw==", "dependencies": {}}, "date-fns@3.6.0": {"integrity": "sha512-fRHTG8g/Gif+kSh50gaGEdToemgfj74aRX3swtiouboip5JDLAyDE9F11nHMIcvOaXeOC6D7SpNhi7uFyB7Uww==", "dependencies": {}}, "debug@4.3.6": {"integrity": "sha512-O/09Bd4Z1fBrU4VzkhFqVgpPzaGbw6Sm9FEkBT1A/YBXQFGuuSxa1dN2nxgxS34JmKXqYx8CZAwEVoJFImUXIg==", "dependencies": {"ms": "ms@2.1.2"}}, "decode-uri-component@0.2.2": {"integrity": "sha512-FqUYQ+8o158GyGTrMFJms9qh3CqTKvAqgqsTnkLI8sKu0028orqBhxNMFkFen0zGyg6epACD32pjVk58ngIErQ==", "dependencies": {}}, "define-data-property@1.1.4": {"integrity": "sha512-rBMvIzlpA8v6E+SJZoo++HAYqsLrkg7MSfIinMPFhmkorw7X+dOXVJQs+QT69zGkzMyfDnIMN2Wid1+NbL3T+A==", "dependencies": {"es-define-property": "es-define-property@1.0.0", "es-errors": "es-errors@1.3.0", "gopd": "gopd@1.0.1"}}, "define-properties@1.2.1": {"integrity": "sha512-8QmQKqEASLd5nx0U1B1okLElbUuuttJ/AnYmRXbbbGDWh6uS208EjD4Xqq/I9wK7u0v6O08XhTWnt5XtEbR6Dg==", "dependencies": {"define-data-property": "define-data-property@1.1.4", "has-property-descriptors": "has-property-descriptors@1.0.2", "object-keys": "object-keys@1.1.1"}}, "delayed-stream@1.0.0": {"integrity": "sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ==", "dependencies": {}}, "dom-helpers@5.2.1": {"integrity": "sha512-nRCa7CK3VTrM2NmGkIy4cbK7IZlgBE/PYMn55rrXefr5xXDP0LdtfPnblFDoVdcAfslJ7or6iqAUnx0CCGIWQA==", "dependencies": {"@babel/runtime": "@babel/runtime@7.25.0", "csstype": "csstype@3.1.3"}}, "dompurify@2.5.6": {"integrity": "sha512-zUTaUBO8pY4+iJMPE1B9XlO2tXVYIcEA4SNGtvDELzTSCQO7RzH+j7S180BmhmJId78lqGU2z19vgVx2Sxs/PQ==", "dependencies": {}}, "error-ex@1.3.2": {"integrity": "sha512-7dFHNmqeFSEt2ZBsCriorKnn3Z2pj+fd9kmI6QoWw4//DL+icEBfc0U7qJCisqrTsKTjw4fNFy2pW9OqStD84g==", "dependencies": {"is-arrayish": "is-arrayish@0.2.1"}}, "es-define-property@1.0.0": {"integrity": "sha512-jxayLKShrEqqzJ0eumQbVhTYQM27CfT1T35+gCgDFoL82JLsXqTJ76zv6A0YLOgEnLUMvLzsDsGIrl8NFpT2gQ==", "dependencies": {"get-intrinsic": "get-intrinsic@1.2.4"}}, "es-errors@1.3.0": {"integrity": "sha512-Zf5H2Kxt2xjTvbJvP2ZWLEICxA6j+hAmMzIlypy4xcBg1vKVnx89Wy0GbS+kf5cwCVFFzdCFh2XSCFNULS6csw==", "dependencies": {}}, "es-object-atoms@1.0.0": {"integrity": "sha512-MZ4iQ6JwHOBQjahnjwaC1ZtIBH+2ohjamzAO3oaHcXYup7qxjF2fixyH+Q71voWHeOkI2q/TnJao/KfXYIZWbw==", "dependencies": {"es-errors": "es-errors@1.3.0"}}, "escape-string-regexp@1.0.5": {"integrity": "sha512-vbRorB5FUQWvla16U8R/qgaFIya2qGzwDrNmCZuYKrbdSUMG6I1ZCGQRefkRVhuOkIGVne7BQ35DSfo1qvJqFg==", "dependencies": {}}, "escape-string-regexp@4.0.0": {"integrity": "sha512-TtpcNJ3XAzx3Gq8sWRzJaVajRs0uVxA2YAkdb1jm2YkPz4G6egUFAyA3n5vtEIZefPk5Wa4UXbKuS5fKkJWdgA==", "dependencies": {}}, "event-target-shim@5.0.1": {"integrity": "sha512-i/2XbnSz/uxRCU6+NdVJgKWDTM427+MqYbkQzD321DuCQJUqOuJKIA0IM2+W2xtYHdKOmZ4dR6fExsd4SXL+WQ==", "dependencies": {}}, "eventemitter3@5.0.1": {"integrity": "sha512-GWkBvjiSZK87ELrYOSESUYeVIc9mvLLf/nXalMOS5dYrgZq9o5OVkbZAVM06CVxYsCwH9BDZFPlQTlPA1j4ahA==", "dependencies": {}}, "file-selector@0.6.0": {"integrity": "sha512-QlZ5yJC0VxHxQQsQhXvBaC7VRJ2uaxTf+Tfpu4Z/OcVQJVpZO+DGU0rkoVW5ce2SccxugvpBJoMvUs59iILYdw==", "dependencies": {"tslib": "tslib@2.6.3"}}, "filter-obj@1.1.0": {"integrity": "sha512-8rXg1ZnX7xzy2NGDVkBVaAy+lSlPNwad13BtgSlLuxfIslyt5Vg64U7tFcCt4WS1R0hvtnQybT/IyCkGZ3DpXQ==", "dependencies": {}}, "find-root@1.1.0": {"integrity": "sha512-NKfW6bec6GfKc0SGx1e07QZY9PE99u0Bft/0rzSD5k3sO/vwkVUpDUKVm5Gpp5Ue3YfShPFTX2070tDs5kB9Ng==", "dependencies": {}}, "form-data-encoder@1.7.2": {"integrity": "sha512-qfqtYan3rxrnCk1VYaA4H+Ms9xdpPqvLZa6xmMgFvhO32x7/3J/ExcTd6qpxM0vH2GdMI+poehyBZvqfMTto8A==", "dependencies": {}}, "form-data@4.0.0": {"integrity": "sha512-ETEklSGi5t0QMZuiXoA/Q6vcnxcLQP5vdugSpuAyi6SVGi2clPPp+xgEhuMaHC+zGgn31Kd235W35f7Hykkaww==", "dependencies": {"asynckit": "asynckit@0.4.0", "combined-stream": "combined-stream@1.0.8", "mime-types": "mime-types@2.1.35"}}, "formdata-node@4.4.1": {"integrity": "sha512-0iirZp3uVDjVGt9p49aTaqjk84TrglENEDuqfdlZQ1roC9CWlPk6Avf8EEnZNcAqPonwkG35x4n3ww/1THYAeQ==", "dependencies": {"node-domexception": "node-domexception@1.0.0", "web-streams-polyfill": "web-streams-polyfill@4.0.0-beta.3"}}, "function-bind@1.1.2": {"integrity": "sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==", "dependencies": {}}, "get-intrinsic@1.2.4": {"integrity": "sha512-5uYhsJH8VJBTv7oslg4BznJYhDoRI6waYCxMmCdnTrcCrHA/fCFKoTFz2JKKE0HdDFUF7/oQuhzumXJK7paBRQ==", "dependencies": {"es-errors": "es-errors@1.3.0", "function-bind": "function-bind@1.1.2", "has-proto": "has-proto@1.0.3", "has-symbols": "has-symbols@1.0.3", "hasown": "hasown@2.0.2"}}, "globals@11.12.0": {"integrity": "sha512-WOBp/EEGUiIsJSp7wcv/y6MO+lV9UoncWqxuFfm8eBwzWNgyfBd6Gz+IeKQ9jCmyhoH99g15M3T+QaVHFjizVA==", "dependencies": {}}, "gopd@1.0.1": {"integrity": "sha512-d65bNlIadxvpb/A2abVdlqKqV563juRnZ1Wtk6s1sIR8uNsXR70xqIzVqxVf1eTqDunwT2MkczEeaezCKTZhwA==", "dependencies": {"get-intrinsic": "get-intrinsic@1.2.4"}}, "has-flag@3.0.0": {"integrity": "sha512-sKJf1+ceQBr4SMkvQnBDNDtf4TXpVhVGateu0t918bl30FnbE2m4vNLX+VWe/dpjlb+HugGYzW7uQXH98HPEYw==", "dependencies": {}}, "has-property-descriptors@1.0.2": {"integrity": "sha512-55JNKuIW+vq4Ke1BjOTjM2YctQIvCT7GFzHwmfZPGo5wnrgkid0YQtnAleFSqumZm4az3n2BS+erby5ipJdgrg==", "dependencies": {"es-define-property": "es-define-property@1.0.0"}}, "has-proto@1.0.3": {"integrity": "sha512-SJ1amZAJUiZS+PhsVLf5tGydlaVB8EdFpaSO4gmiUKUOxk8qzn5AIy4ZeJUmh22znIdk/uMAUT2pl3FxzVUH+Q==", "dependencies": {}}, "has-symbols@1.0.3": {"integrity": "sha512-l3LCuF6MgDNwTDKkdYGEihYjt5pRPbEg46rtlmnSPlUbgmB8LOIrKJbYYFBSbnPaJexMKtiPO8hmeRjRz2Td+A==", "dependencies": {}}, "hasown@2.0.2": {"integrity": "sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==", "dependencies": {"function-bind": "function-bind@1.1.2"}}, "hoist-non-react-statics@3.3.2": {"integrity": "sha512-/gGivxi8JPKWNm/W0jSmzcMPpfpPLc3dY/6GxhX2hQ9iGj3aDfklV4ET7NjKpSinLpJ5vafa9iiGIEZg10SfBw==", "dependencies": {"react-is": "react-is@16.13.1"}}, "hotscript@1.0.13": {"integrity": "sha512-C++tTF1GqkGYecL+2S1wJTfoH6APGAsbb7PAWQ3iVIwgG/EFseAfEVOKFgAFq4yK3+6j1EjUD4UQ9dRJHX/sSQ==", "dependencies": {}}, "humanize-ms@1.2.1": {"integrity": "sha512-Fl70vYtsAFb/C06PTS9dZBo7ihau+Tu/DNCk/OyHhea07S+aeMWpFFkUaXRa8fI+ScZbEI8dfSxwY7gxZ9SAVQ==", "dependencies": {"ms": "ms@2.1.3"}}, "import-fresh@3.3.0": {"integrity": "sha512-veYYhQa+D1QBKznvhUHxb8faxlrwUnxseDAbAp457E0wLNio2bOSKnjYDhMj+YiAq61xrMGhQk9iXVk5FzgQMw==", "dependencies": {"parent-module": "parent-module@1.0.1", "resolve-from": "resolve-from@4.0.0"}}, "inflection@3.0.0": {"integrity": "sha512-1zEJU1l19SgJlmwqsEyFTbScw/tkMHFenUo//Y0i+XEP83gDFdMvPizAD/WGcE+l1ku12PcTVHQhO6g5E0UCMw==", "dependencies": {}}, "is-arrayish@0.2.1": {"integrity": "sha512-zz06S8t0ozoDXMG+ube26zeCTNXcKIPJZJi8hBrF4idCLms4CG9QtK7qBl1boi5ODzFpjswb5JPmHCbMpjaYzg==", "dependencies": {}}, "is-core-module@2.15.0": {"integrity": "sha512-Dd+Lb2/zvk9SKy1TGCt1wFJFo/MWBPMX5x7KcvLajWTGuomczdQX61PvY5yK6SVACwpoexWo81IfFyoKY2QnTA==", "dependencies": {"hasown": "hasown@2.0.2"}}, "js-tokens@4.0.0": {"integrity": "sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==", "dependencies": {}}, "jsesc@2.5.2": {"integrity": "sha512-OYu7XEzjkCQ3C5Ps3QIZsQfNpqoJyZZA99wd9aWd05NCtC5pWOkShK2mkL6HXQR6/Cy2lbNdPlZBpuQHXE63gA==", "dependencies": {}}, "json-parse-even-better-errors@2.3.1": {"integrity": "sha512-xyFwyhro/JEof6Ghe2iz2NcXoj2sloNsWr/XsERDK/oiPCfaNhl5ONfp+jQdAZRQQ0IJWNzH9zIZF7li91kh2w==", "dependencies": {}}, "jsonexport@3.2.0": {"integrity": "sha512-GbO9ugb0YTZatPd/hqCGR0FSwbr82H6OzG04yzdrG7XOe4QZ0jhQ+kOsB29zqkzoYJLmLxbbrFiuwbQu891XnQ==", "dependencies": {}}, "lines-and-columns@1.2.4": {"integrity": "sha512-7ylylesZQ/PV29jhEDl3Ufjo6ZX7gCqJr5F7PKrqc93v7fzSymt1BpwEU8nAUXs8qzzvqhbjhK5QZg6Mt/HkBg==", "dependencies": {}}, "lodash.isequal@4.5.0": {"integrity": "sha512-pDo3lu8Jhfjqls6GkMgpahsF9kCyayhgykjyLMNFTKWrpVdAQtYyB4muAMWozBB4ig/dtWAmsMxLEI8wuz+DYQ==", "dependencies": {}}, "lodash@4.17.21": {"integrity": "sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==", "dependencies": {}}, "loose-envify@1.4.0": {"integrity": "sha512-lyuxPGr/Wfhrlem2CL/UcnUc1zcqKAImBDzukY7Y5F/yQiNdko6+fRLevlw1HgMySw7f611UIY408EtxRSoK3Q==", "dependencies": {"js-tokens": "js-tokens@4.0.0"}}, "mime-db@1.52.0": {"integrity": "sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==", "dependencies": {}}, "mime-types@2.1.35": {"integrity": "sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==", "dependencies": {"mime-db": "mime-db@1.52.0"}}, "ms@2.1.2": {"integrity": "sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w==", "dependencies": {}}, "ms@2.1.3": {"integrity": "sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==", "dependencies": {}}, "node-domexception@1.0.0": {"integrity": "sha512-/jKZoMpw0F8GRwl4/eLROPA3cfcXtLApP0QzLmUT/HuPCZWyB7IY9ZrMeKw2O/nFIqPQB3PVM9aYm0F312AXDQ==", "dependencies": {}}, "node-fetch@2.7.0": {"integrity": "sha512-c4FRfUm/dbcWZ7U+1Wq0AwCyFL+3nt2bEw05wfxSz+DWpWsitgmSgYmy2dQdWyKC1694ELPqMs/YzUSNozLt8A==", "dependencies": {"whatwg-url": "whatwg-url@5.0.0"}}, "node-polyglot@2.6.0": {"integrity": "sha512-ZZFkaYzIfGfBvSM6QhA9dM8EEaUJOVewzGSRcXWbJELXDj0lajAtKaENCYxvF5yE+TgHg6NQb0CmgYMsMdcNJQ==", "dependencies": {"hasown": "hasown@2.0.2", "object.entries": "object.entries@1.1.8", "warning": "warning@4.0.3"}}, "object-assign@4.1.1": {"integrity": "sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg==", "dependencies": {}}, "object-inspect@1.13.2": {"integrity": "sha512-IRZSRuzJiynemAXPYtPe5BoI/RESNYR7TYm50MC5Mqbd3Jmw5y790sErYw3V6SryFJD64b74qQQs9wn5Bg/k3g==", "dependencies": {}}, "object-keys@1.1.1": {"integrity": "sha512-NuAESUOUMrlIXOfHKzD6bpPu3tYt3xvjNdRIQ+FeT0lNb4K8WR70CaDxhuNguS2XG+GjkyMwOzsN5ZktImfhLA==", "dependencies": {}}, "object.entries@1.1.8": {"integrity": "sha512-cmopxi8VwRIAw/fkijJohSfpef5PdN0pMQJN6VC/ZKvn0LIknWD8KtgY6KlQdEc4tIjcQ3HxSMmnvtzIscdaYQ==", "dependencies": {"call-bind": "call-bind@1.0.7", "define-properties": "define-properties@1.2.1", "es-object-atoms": "es-object-atoms@1.0.0"}}, "openai@4.54.0": {"integrity": "sha512-e/12BdtTtj+tXs7iHm+Dm7H7WjEWnw7O52B2wSfCQ6lD5F6cvjzo7cANXy5TJ1Q3/qc8YRPT5wBTTFtP5sBp1g==", "dependencies": {"@types/node": "@types/node@18.19.43", "@types/node-fetch": "@types/node-fetch@2.6.11", "abort-controller": "abort-controller@3.0.0", "agentkeepalive": "agentkeepalive@4.5.0", "form-data-encoder": "form-data-encoder@1.7.2", "formdata-node": "formdata-node@4.4.1", "node-fetch": "node-fetch@2.7.0"}}, "parent-module@1.0.1": {"integrity": "sha512-GQ2EWRpQV8/o+Aw8YqtfZZPfNRWZYkbidE9k5rpl/hC3vtHHBfGm2Ifi6qWV+coDGkrUKZAxE3Lot5kcsRlh+g==", "dependencies": {"callsites": "callsites@3.1.0"}}, "parse-json@5.2.0": {"integrity": "sha512-ayCKvm/phCGxOkYRSCM82iDwct8/EonSEgCSxWxD7ve6jHggsFl4fZVQBPRNgQoKiuV/odhFrGzQXZwbifC8Rg==", "dependencies": {"@babel/code-frame": "@babel/code-frame@7.24.7", "error-ex": "error-ex@1.3.2", "json-parse-even-better-errors": "json-parse-even-better-errors@2.3.1", "lines-and-columns": "lines-and-columns@1.2.4"}}, "path-parse@1.0.7": {"integrity": "sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw==", "dependencies": {}}, "path-type@4.0.0": {"integrity": "sha512-gDKb8aZMDeD/tZWs9P6+q0J9Mwkdl6xMV8TjnGP3qJVJ06bdMgkbBlLU8IdfOsIsFz2BW1rNVT3XuNEl8zPAvw==", "dependencies": {}}, "picocolors@1.0.1": {"integrity": "sha512-anP1Z8qwhkbmu7MFP5iTt+wQKXgwzf7zTyGlcdzabySa9vd0Xt392U0rVmz9poOaBj0uHJKyyo9/upk0HrEQew==", "dependencies": {}}, "prop-types@15.8.1": {"integrity": "sha512-oj87CgZICdulUohogVAR7AjlC0327U4el4L6eAvOqCeudMDVU0NThNaV+b9Df4dXgSP1gXMTnPdhfe/2qDH5cg==", "dependencies": {"loose-envify": "loose-envify@1.4.0", "object-assign": "object-assign@4.1.1", "react-is": "react-is@16.13.1"}}, "qs@6.13.0": {"integrity": "sha512-+38qI9SOr8tfZ4QmJNplMUxqjbe7LKvvZgWdExBOmd+egZTtjLB67Gu0HRX3u/XOq7UU2Nx6nsjvS16Z9uwfpg==", "dependencies": {"side-channel": "side-channel@1.0.6"}}, "query-string@7.1.3": {"integrity": "sha512-hh2WYhq4fi8+b+/2Kg9CEge4fDPvHS534aOOvOZeQ3+Vf2mCFsaFBYj0i+iXcAq6I9Vzp5fjMFBlONvayDC1qg==", "dependencies": {"decode-uri-component": "decode-uri-component@0.2.2", "filter-obj": "filter-obj@1.1.0", "split-on-first": "split-on-first@1.1.0", "strict-uri-encode": "strict-uri-encode@2.0.0"}}, "ra-core@5.1.1_react@18.3.1_react-dom@18.3.1__react@18.3.1_react-hook-form@7.52.2__react@18.3.1_react-router@6.26.0__react@18.3.1_react-router-dom@6.26.0__react@18.3.1__react-dom@18.3.1___react@18.3.1": {"integrity": "sha512-b0DR83y<PERSON>ZscWLNIoSNh61gWEySCLbtBuR7c74i6WCVGi4uJEAr+1+nn+1UYBPRPg3hMjMud672PRMwu5iLioaQ==", "dependencies": {"@tanstack/react-query": "@tanstack/react-query@5.51.21_react@18.3.1", "clsx": "clsx@2.1.1", "date-fns": "date-fns@3.6.0", "eventemitter3": "eventemitter3@5.0.1", "hotscript": "hotscript@1.0.13", "inflection": "inflection@3.0.0", "jsonexport": "jsonexport@3.2.0", "lodash": "lodash@4.17.21", "query-string": "query-string@7.1.3", "react": "react@18.3.1", "react-dom": "react-dom@18.3.1_react@18.3.1", "react-error-boundary": "react-error-boundary@4.0.13_react@18.3.1", "react-hook-form": "react-hook-form@7.52.2_react@18.3.1", "react-is": "react-is@18.3.1", "react-router": "react-router@6.26.0_react@18.3.1", "react-router-dom": "react-router-dom@6.26.0_react@18.3.1_react-dom@18.3.1__react@18.3.1"}}, "ra-i18n-polyglot@5.1.1_react@18.3.1_react-dom@18.3.1__react@18.3.1_react-hook-form@7.52.2__react@18.3.1_react-router@6.26.0__react@18.3.1_react-router-dom@6.26.0__react@18.3.1__react-dom@18.3.1___react@18.3.1": {"integrity": "sha512-ckzJy9WLCMBaSMk4Tgknf+/rRQ1R8ghXpVRM2gCzYlBI1lYDGk9qh0PMXk3FC7u6fCADy2q0T1KGfJNbalzZgg==", "dependencies": {"node-polyglot": "node-polyglot@2.6.0", "ra-core": "ra-core@5.1.1_react@18.3.1_react-dom@18.3.1__react@18.3.1_react-hook-form@7.52.2__react@18.3.1_react-router@6.26.0__react@18.3.1_react-router-dom@6.26.0__react@18.3.1__react-dom@18.3.1___react@18.3.1"}}, "ra-language-english@5.1.1_react@18.3.1_react-dom@18.3.1__react@18.3.1_react-hook-form@7.52.2__react@18.3.1_react-router@6.26.0__react@18.3.1_react-router-dom@6.26.0__react@18.3.1__react-dom@18.3.1___react@18.3.1": {"integrity": "sha512-AB579oGAvhb/usuiXBJnVk4WKP3tIclBsVwTqBwfgd4bSg9GLQEoqZn53WPCBfIgyO7sclfKz1h1Wh5F7IqQWw==", "dependencies": {"ra-core": "ra-core@5.1.1_react@18.3.1_react-dom@18.3.1__react@18.3.1_react-hook-form@7.52.2__react@18.3.1_react-router@6.26.0__react@18.3.1_react-router-dom@6.26.0__react@18.3.1__react-dom@18.3.1___react@18.3.1"}}, "ra-supabase-core@3.0.0_@raphiniert+ra-data-postgrest@2.3.0__ra-core@5.1.1___react@18.3.1___react-dom@18.3.1____react@18.3.1___react-hook-form@7.52.2____react@18.3.1___react-router@6.26.0____react@18.3.1___react-router-dom@6.26.0____react@18.3.1____react-dom@18.3.1_____react@18.3.1__react@18.3.1_@supabase+supabase-js@2.45.1_ra-core@5.1.1__react@18.3.1__react-dom@18.3.1___react@18.3.1__react-hook-form@7.52.2___react@18.3.1__react-router@6.26.0___react@18.3.1__react-router-dom@6.26.0___react@18.3.1___react-dom@18.3.1____react@18.3.1_react@18.3.1": {"integrity": "sha512-V6OHsgOE7icqH6ZstwOjny55mxPeSh2mmAdsUnuiYORk6mWAq8g7d5ba8pZHhowXKiUK3G0Pw9VEqMVIZ55y2g==", "dependencies": {"@raphiniert/ra-data-postgrest": "@raphiniert/ra-data-postgrest@2.3.0_ra-core@5.1.1__react@18.3.1__react-dom@18.3.1___react@18.3.1__react-hook-form@7.52.2___react@18.3.1__react-router@6.26.0___react@18.3.1__react-router-dom@6.26.0___react@18.3.1___react-dom@18.3.1____react@18.3.1_react@18.3.1", "@supabase/supabase-js": "@supabase/supabase-js@2.45.1", "ra-core": "ra-core@5.1.1_react@18.3.1_react-dom@18.3.1__react@18.3.1_react-hook-form@7.52.2__react@18.3.1_react-router@6.26.0__react@18.3.1_react-router-dom@6.26.0__react@18.3.1__react-dom@18.3.1___react@18.3.1"}}, "ra-supabase-core@3.0.0_@raphiniert+ra-data-postgrest@2.3.0__ra-core@5.1.1___react@18.3.1___react-dom@18.3.1____react@18.3.1___react-hook-form@7.52.2____react@18.3.1___react-router@6.26.0____react@18.3.1___react-router-dom@6.26.0____react@18.3.1____react-dom@18.3.1_____react@18.3.1__react@18.3.1_@supabase+supabase-js@2.45.1_ra-core@5.1.1__react@18.3.1__react-dom@18.3.1___react@18.3.1__react-hook-form@7.52.2___react@18.3.1__react-router@6.26.0___react@18.3.1__react-router-dom@6.26.0___react@18.3.1___react-dom@18.3.1____react@18.3.1_react@18.3.1_react-dom@18.3.1__react@18.3.1_react-router@6.26.0__react@18.3.1": {"integrity": "sha512-V6OHsgOE7icqH6ZstwOjny55mxPeSh2mmAdsUnuiYORk6mWAq8g7d5ba8pZHhowXKiUK3G0Pw9VEqMVIZ55y2g==", "dependencies": {"@raphiniert/ra-data-postgrest": "@raphiniert/ra-data-postgrest@2.3.0_ra-core@5.1.1__react@18.3.1__react-dom@18.3.1___react@18.3.1__react-hook-form@7.52.2___react@18.3.1__react-router@6.26.0___react@18.3.1__react-router-dom@6.26.0___react@18.3.1___react-dom@18.3.1____react@18.3.1_react@18.3.1_react-dom@18.3.1__react@18.3.1_react-router@6.26.0__react@18.3.1", "@supabase/supabase-js": "@supabase/supabase-js@2.45.1", "ra-core": "ra-core@5.1.1_react@18.3.1_react-dom@18.3.1__react@18.3.1_react-hook-form@7.52.2__react@18.3.1_react-router@6.26.0__react@18.3.1_react-router-dom@6.26.0__react@18.3.1__react-dom@18.3.1___react@18.3.1"}}, "ra-supabase-language-english@3.0.0": {"integrity": "sha512-V1VJrUnAZWTys1s7Qv7xJx55mxDrgui7E22NtNPe+ET+TROsLs7xtDZmsWcmMDTOpqnzuqyPS5x/zmJDJ7/uxw==", "dependencies": {}}, "ra-supabase-ui-materialui@3.0.0_@mui+icons-material@5.16.6__@mui+material@5.16.6___react@18.3.1___react-dom@18.3.1____react@18.3.1___@emotion+react@11.13.0____react@18.3.1___@emotion+styled@11.13.0____@emotion+react@11.13.0_____react@18.3.1____react@18.3.1__react@18.3.1__react-dom@18.3.1___react@18.3.1_@mui+material@5.16.6__react@18.3.1__react-dom@18.3.1___react@18.3.1__@emotion+react@11.13.0___react@18.3.1__@emotion+styled@11.13.0___@emotion+react@11.13.0____react@18.3.1___react@18.3.1_@supabase+supabase-js@2.45.1_ra-core@5.1.1__react@18.3.1__react-dom@18.3.1___react@18.3.1__react-hook-form@7.52.2___react@18.3.1__react-router@6.26.0___react@18.3.1__react-router-dom@6.26.0___react@18.3.1___react-dom@18.3.1____react@18.3.1_ra-ui-materialui@5.1.1__@mui+icons-material@5.16.6___@mui+material@5.16.6____react@18.3.1____react-dom@18.3.1_____react@18.3.1____@emotion+react@11.13.0_____react@18.3.1____@emotion+styled@11.13.0_____@emotion+react@11.13.0______react@18.3.1_____react@18.3.1___react@18.3.1___react-dom@18.3.1____react@18.3.1__@mui+material@5.16.6___react@18.3.1___react-dom@18.3.1____react@18.3.1___@emotion+react@11.13.0____react@18.3.1___@emotion+styled@11.13.0____@emotion+react@11.13.0_____react@18.3.1____react@18.3.1__ra-core@5.1.1___react@18.3.1___react-dom@18.3.1____react@18.3.1___react-hook-form@7.52.2____react@18.3.1___react-router@6.26.0____react@18.3.1___react-router-dom@6.26.0____react@18.3.1____react-dom@18.3.1_____react@18.3.1__react@18.3.1__react-dom@18.3.1___react@18.3.1__react-hook-form@7.52.2___react@18.3.1__react-is@18.3.1__react-router@6.26.0___react@18.3.1__react-router-dom@6.26.0___react@18.3.1___react-dom@18.3.1____react@18.3.1_react@18.3.1_react-dom@18.3.1__react@18.3.1_react-router@6.26.0__react@18.3.1": {"integrity": "sha512-BcZr4AeqCfSLpc+fUWaaKzw+F5xI3bp9nBmSuk+eoUW7c67uDqY0eSi7kO5HmiM6WoCnerfhrDrljAs4JKMavg==", "dependencies": {"@mui/icons-material": "@mui/icons-material@5.16.6_@mui+material@5.16.6__react@18.3.1__react-dom@18.3.1___react@18.3.1__@emotion+react@11.13.0___react@18.3.1__@emotion+styled@11.13.0___@emotion+react@11.13.0____react@18.3.1___react@18.3.1_react@18.3.1_react-dom@18.3.1__react@18.3.1", "@mui/material": "@mui/material@5.16.6_react@18.3.1_react-dom@18.3.1__react@18.3.1_@emotion+react@11.13.0__react@18.3.1_@emotion+styled@11.13.0__@emotion+react@11.13.0___react@18.3.1__react@18.3.1", "@supabase/supabase-js": "@supabase/supabase-js@2.45.1", "ra-core": "ra-core@5.1.1_react@18.3.1_react-dom@18.3.1__react@18.3.1_react-hook-form@7.52.2__react@18.3.1_react-router@6.26.0__react@18.3.1_react-router-dom@6.26.0__react@18.3.1__react-dom@18.3.1___react@18.3.1", "ra-supabase-core": "ra-supabase-core@3.0.0_@raphiniert+ra-data-postgrest@2.3.0__ra-core@5.1.1___react@18.3.1___react-dom@18.3.1____react@18.3.1___react-hook-form@7.52.2____react@18.3.1___react-router@6.26.0____react@18.3.1___react-router-dom@6.26.0____react@18.3.1____react-dom@18.3.1_____react@18.3.1__react@18.3.1_@supabase+supabase-js@2.45.1_ra-core@5.1.1__react@18.3.1__react-dom@18.3.1___react@18.3.1__react-hook-form@7.52.2___react@18.3.1__react-router@6.26.0___react@18.3.1__react-router-dom@6.26.0___react@18.3.1___react-dom@18.3.1____react@18.3.1_react@18.3.1_react-dom@18.3.1__react@18.3.1_react-router@6.26.0__react@18.3.1", "ra-ui-materialui": "ra-ui-materialui@5.1.1_@mui+icons-material@5.16.6__@mui+material@5.16.6___react@18.3.1___react-dom@18.3.1____react@18.3.1___@emotion+react@11.13.0____react@18.3.1___@emotion+styled@11.13.0____@emotion+react@11.13.0_____react@18.3.1____react@18.3.1__react@18.3.1__react-dom@18.3.1___react@18.3.1_@mui+material@5.16.6__react@18.3.1__react-dom@18.3.1___react@18.3.1__@emotion+react@11.13.0___react@18.3.1__@emotion+styled@11.13.0___@emotion+react@11.13.0____react@18.3.1___react@18.3.1_ra-core@5.1.1__react@18.3.1__react-dom@18.3.1___react@18.3.1__react-hook-form@7.52.2___react@18.3.1__react-router@6.26.0___react@18.3.1__react-router-dom@6.26.0___react@18.3.1___react-dom@18.3.1____react@18.3.1_react@18.3.1_react-dom@18.3.1__react@18.3.1_react-hook-form@7.52.2__react@18.3.1_react-is@18.3.1_react-router@6.26.0__react@18.3.1_react-router-dom@6.26.0__react@18.3.1__react-dom@18.3.1___react@18.3.1", "react": "react@18.3.1", "react-dom": "react-dom@18.3.1_react@18.3.1", "react-router": "react-router@6.26.0_react@18.3.1"}}, "ra-supabase@3.0.0_@mui+material@5.16.6__react@18.3.1__react-dom@18.3.1___react@18.3.1__@emotion+react@11.13.0___react@18.3.1__@emotion+styled@11.13.0___@emotion+react@11.13.0____react@18.3.1___react@18.3.1_react@18.3.1": {"integrity": "sha512-vBVPyPkPhlfuAJp5CnQseNioBcpqGjfz9MOuOekXsRRXA+5dv8nJfp9XnX7wytGU4oax+gRNqlXJMZJEtZDJXQ==", "dependencies": {"ra-supabase-core": "ra-supabase-core@3.0.0_@raphiniert+ra-data-postgrest@2.3.0__ra-core@5.1.1___react@18.3.1___react-dom@18.3.1____react@18.3.1___react-hook-form@7.52.2____react@18.3.1___react-router@6.26.0____react@18.3.1___react-router-dom@6.26.0____react@18.3.1____react-dom@18.3.1_____react@18.3.1__react@18.3.1_@supabase+supabase-js@2.45.1_ra-core@5.1.1__react@18.3.1__react-dom@18.3.1___react@18.3.1__react-hook-form@7.52.2___react@18.3.1__react-router@6.26.0___react@18.3.1__react-router-dom@6.26.0___react@18.3.1___react-dom@18.3.1____react@18.3.1_react@18.3.1", "ra-supabase-language-english": "ra-supabase-language-english@3.0.0", "ra-supabase-ui-materialui": "ra-supabase-ui-materialui@3.0.0_@mui+icons-material@5.16.6__@mui+material@5.16.6___react@18.3.1___react-dom@18.3.1____react@18.3.1___@emotion+react@11.13.0____react@18.3.1___@emotion+styled@11.13.0____@emotion+react@11.13.0_____react@18.3.1____react@18.3.1__react@18.3.1__react-dom@18.3.1___react@18.3.1_@mui+material@5.16.6__react@18.3.1__react-dom@18.3.1___react@18.3.1__@emotion+react@11.13.0___react@18.3.1__@emotion+styled@11.13.0___@emotion+react@11.13.0____react@18.3.1___react@18.3.1_@supabase+supabase-js@2.45.1_ra-core@5.1.1__react@18.3.1__react-dom@18.3.1___react@18.3.1__react-hook-form@7.52.2___react@18.3.1__react-router@6.26.0___react@18.3.1__react-router-dom@6.26.0___react@18.3.1___react-dom@18.3.1____react@18.3.1_ra-ui-materialui@5.1.1__@mui+icons-material@5.16.6___@mui+material@5.16.6____react@18.3.1____react-dom@18.3.1_____react@18.3.1____@emotion+react@11.13.0_____react@18.3.1____@emotion+styled@11.13.0_____@emotion+react@11.13.0______react@18.3.1_____react@18.3.1___react@18.3.1___react-dom@18.3.1____react@18.3.1__@mui+material@5.16.6___react@18.3.1___react-dom@18.3.1____react@18.3.1___@emotion+react@11.13.0____react@18.3.1___@emotion+styled@11.13.0____@emotion+react@11.13.0_____react@18.3.1____react@18.3.1__ra-core@5.1.1___react@18.3.1___react-dom@18.3.1____react@18.3.1___react-hook-form@7.52.2____react@18.3.1___react-router@6.26.0____react@18.3.1___react-router-dom@6.26.0____react@18.3.1____react-dom@18.3.1_____react@18.3.1__react@18.3.1__react-dom@18.3.1___react@18.3.1__react-hook-form@7.52.2___react@18.3.1__react-is@18.3.1__react-router@6.26.0___react@18.3.1__react-router-dom@6.26.0___react@18.3.1___react-dom@18.3.1____react@18.3.1_react@18.3.1_react-dom@18.3.1__react@18.3.1_react-router@6.26.0__react@18.3.1"}}, "ra-ui-materialui@5.1.1_@mui+icons-material@5.16.6__@mui+material@5.16.6___@emotion+react@11.13.0____react@18.3.1___@emotion+styled@11.13.0____@emotion+react@11.13.0_____react@18.3.1____react@18.3.1___react@18.3.1___react-dom@18.3.1____react@18.3.1__react@18.3.1__@emotion+react@11.13.0___react@18.3.1__@emotion+styled@11.13.0___@emotion+react@11.13.0____react@18.3.1___react@18.3.1__react-dom@18.3.1___react@18.3.1_@mui+material@5.16.6__@emotion+react@11.13.0___react@18.3.1__@emotion+styled@11.13.0___@emotion+react@11.13.0____react@18.3.1___react@18.3.1__react@18.3.1__react-dom@18.3.1___react@18.3.1_ra-core@5.1.1__react@18.3.1__react-dom@18.3.1___react@18.3.1__react-hook-form@7.52.2___react@18.3.1__react-router@6.26.0___react@18.3.1__react-router-dom@6.26.0___react@18.3.1___react-dom@18.3.1____react@18.3.1_react@18.3.1_react-dom@18.3.1__react@18.3.1_react-hook-form@7.52.2__react@18.3.1_react-is@18.3.1_react-router@6.26.0__react@18.3.1_react-router-dom@6.26.0__react@18.3.1__react-dom@18.3.1___react@18.3.1_@emotion+react@11.13.0__react@18.3.1_@emotion+styled@11.13.0__@emotion+react@11.13.0___react@18.3.1__react@18.3.1": {"integrity": "sha512-A1MaqPKPJ5KJYIVKhw8+3q2O4zV6LjK96Nfs+GmST0/uYPa4osxsujDwz+Kk6Br+RT0u4xM9fpBMw5KceHwoZw==", "dependencies": {"@mui/icons-material": "@mui/icons-material@5.16.6_@mui+material@5.16.6__@emotion+react@11.13.0___react@18.3.1__@emotion+styled@11.13.0___@emotion+react@11.13.0____react@18.3.1___react@18.3.1__react@18.3.1__react-dom@18.3.1___react@18.3.1_react@18.3.1_@emotion+react@11.13.0__react@18.3.1_@emotion+styled@11.13.0__@emotion+react@11.13.0___react@18.3.1__react@18.3.1_react-dom@18.3.1__react@18.3.1", "@mui/material": "@mui/material@5.16.6_@emotion+react@11.13.0__react@18.3.1_@emotion+styled@11.13.0__@emotion+react@11.13.0___react@18.3.1__react@18.3.1_react@18.3.1_react-dom@18.3.1__react@18.3.1", "@tanstack/react-query": "@tanstack/react-query@5.51.21_react@18.3.1", "autosuggest-highlight": "autosuggest-highlight@3.3.4", "clsx": "clsx@2.1.1", "css-mediaquery": "css-mediaquery@0.1.2", "dompurify": "dompurify@2.5.6", "hotscript": "hotscript@1.0.13", "inflection": "inflection@3.0.0", "jsonexport": "jsonexport@3.2.0", "lodash": "lodash@4.17.21", "query-string": "query-string@7.1.3", "ra-core": "ra-core@5.1.1_react@18.3.1_react-dom@18.3.1__react@18.3.1_react-hook-form@7.52.2__react@18.3.1_react-router@6.26.0__react@18.3.1_react-router-dom@6.26.0__react@18.3.1__react-dom@18.3.1___react@18.3.1", "react": "react@18.3.1", "react-dom": "react-dom@18.3.1_react@18.3.1", "react-dropzone": "react-dropzone@14.2.3_react@18.3.1", "react-error-boundary": "react-error-boundary@4.0.13_react@18.3.1", "react-hook-form": "react-hook-form@7.52.2_react@18.3.1", "react-is": "react-is@18.3.1", "react-router": "react-router@6.26.0_react@18.3.1", "react-router-dom": "react-router-dom@6.26.0_react@18.3.1_react-dom@18.3.1__react@18.3.1", "react-transition-group": "react-transition-group@4.4.5_react@18.3.1_react-dom@18.3.1__react@18.3.1"}}, "ra-ui-materialui@5.1.1_@mui+icons-material@5.16.6__@mui+material@5.16.6___react@18.3.1___react-dom@18.3.1____react@18.3.1___@emotion+react@11.13.0____react@18.3.1___@emotion+styled@11.13.0____@emotion+react@11.13.0_____react@18.3.1____react@18.3.1__react@18.3.1__react-dom@18.3.1___react@18.3.1_@mui+material@5.16.6__react@18.3.1__react-dom@18.3.1___react@18.3.1__@emotion+react@11.13.0___react@18.3.1__@emotion+styled@11.13.0___@emotion+react@11.13.0____react@18.3.1___react@18.3.1_ra-core@5.1.1__react@18.3.1__react-dom@18.3.1___react@18.3.1__react-hook-form@7.52.2___react@18.3.1__react-router@6.26.0___react@18.3.1__react-router-dom@6.26.0___react@18.3.1___react-dom@18.3.1____react@18.3.1_react@18.3.1_react-dom@18.3.1__react@18.3.1_react-hook-form@7.52.2__react@18.3.1_react-is@18.3.1_react-router@6.26.0__react@18.3.1_react-router-dom@6.26.0__react@18.3.1__react-dom@18.3.1___react@18.3.1": {"integrity": "sha512-A1MaqPKPJ5KJYIVKhw8+3q2O4zV6LjK96Nfs+GmST0/uYPa4osxsujDwz+Kk6Br+RT0u4xM9fpBMw5KceHwoZw==", "dependencies": {"@mui/icons-material": "@mui/icons-material@5.16.6_@mui+material@5.16.6__react@18.3.1__react-dom@18.3.1___react@18.3.1__@emotion+react@11.13.0___react@18.3.1__@emotion+styled@11.13.0___@emotion+react@11.13.0____react@18.3.1___react@18.3.1_react@18.3.1_react-dom@18.3.1__react@18.3.1", "@mui/material": "@mui/material@5.16.6_react@18.3.1_react-dom@18.3.1__react@18.3.1_@emotion+react@11.13.0__react@18.3.1_@emotion+styled@11.13.0__@emotion+react@11.13.0___react@18.3.1__react@18.3.1", "@tanstack/react-query": "@tanstack/react-query@5.51.21_react@18.3.1", "autosuggest-highlight": "autosuggest-highlight@3.3.4", "clsx": "clsx@2.1.1", "css-mediaquery": "css-mediaquery@0.1.2", "dompurify": "dompurify@2.5.6", "hotscript": "hotscript@1.0.13", "inflection": "inflection@3.0.0", "jsonexport": "jsonexport@3.2.0", "lodash": "lodash@4.17.21", "query-string": "query-string@7.1.3", "ra-core": "ra-core@5.1.1_react@18.3.1_react-dom@18.3.1__react@18.3.1_react-hook-form@7.52.2__react@18.3.1_react-router@6.26.0__react@18.3.1_react-router-dom@6.26.0__react@18.3.1__react-dom@18.3.1___react@18.3.1", "react": "react@18.3.1", "react-dom": "react-dom@18.3.1_react@18.3.1", "react-dropzone": "react-dropzone@14.2.3_react@18.3.1", "react-error-boundary": "react-error-boundary@4.0.13_react@18.3.1", "react-hook-form": "react-hook-form@7.52.2_react@18.3.1", "react-is": "react-is@18.3.1", "react-router": "react-router@6.26.0_react@18.3.1", "react-router-dom": "react-router-dom@6.26.0_react@18.3.1_react-dom@18.3.1__react@18.3.1", "react-transition-group": "react-transition-group@4.4.5_react@18.3.1_react-dom@18.3.1__react@18.3.1"}}, "react-admin@5.1.1_react@18.3.1_react-dom@18.3.1__react@18.3.1_@emotion+react@11.13.0__react@18.3.1_@mui+material@5.16.6__@emotion+react@11.13.0___react@18.3.1__@emotion+styled@11.13.0___@emotion+react@11.13.0____react@18.3.1___react@18.3.1__react@18.3.1__react-dom@18.3.1___react@18.3.1_@emotion+styled@11.13.0__@emotion+react@11.13.0___react@18.3.1__react@18.3.1_react-hook-form@7.52.2__react@18.3.1_react-router@6.26.0__react@18.3.1_react-router-dom@6.26.0__react@18.3.1__react-dom@18.3.1___react@18.3.1_@mui+icons-material@5.16.6__@mui+material@5.16.6___@emotion+react@11.13.0____react@18.3.1___@emotion+styled@11.13.0____@emotion+react@11.13.0_____react@18.3.1____react@18.3.1___react@18.3.1___react-dom@18.3.1____react@18.3.1__react@18.3.1__@emotion+react@11.13.0___react@18.3.1__@emotion+styled@11.13.0___@emotion+react@11.13.0____react@18.3.1___react@18.3.1__react-dom@18.3.1___react@18.3.1_ra-core@5.1.1__react@18.3.1__react-dom@18.3.1___react@18.3.1__react-hook-form@7.52.2___react@18.3.1__react-router@6.26.0___react@18.3.1__react-router-dom@6.26.0___react@18.3.1___react-dom@18.3.1____react@18.3.1": {"integrity": "sha512-yImS5Zz2GvJS0qt0tLClFbS85VYGqYkL6Q8giTdljUWTQ8V6W0e4lem7xtciKyyB1DTWcVw8pL6xp3q+lUsieg==", "dependencies": {"@emotion/react": "@emotion/react@11.13.0_react@18.3.1", "@emotion/styled": "@emotion/styled@11.13.0_@emotion+react@11.13.0__react@18.3.1_react@18.3.1", "@mui/icons-material": "@mui/icons-material@5.16.6_@mui+material@5.16.6__@emotion+react@11.13.0___react@18.3.1__@emotion+styled@11.13.0___@emotion+react@11.13.0____react@18.3.1___react@18.3.1__react@18.3.1__react-dom@18.3.1___react@18.3.1_react@18.3.1_@emotion+react@11.13.0__react@18.3.1_@emotion+styled@11.13.0__@emotion+react@11.13.0___react@18.3.1__react@18.3.1_react-dom@18.3.1__react@18.3.1", "@mui/material": "@mui/material@5.16.6_@emotion+react@11.13.0__react@18.3.1_@emotion+styled@11.13.0__@emotion+react@11.13.0___react@18.3.1__react@18.3.1_react@18.3.1_react-dom@18.3.1__react@18.3.1", "ra-core": "ra-core@5.1.1_react@18.3.1_react-dom@18.3.1__react@18.3.1_react-hook-form@7.52.2__react@18.3.1_react-router@6.26.0__react@18.3.1_react-router-dom@6.26.0__react@18.3.1__react-dom@18.3.1___react@18.3.1", "ra-i18n-polyglot": "ra-i18n-polyglot@5.1.1_react@18.3.1_react-dom@18.3.1__react@18.3.1_react-hook-form@7.52.2__react@18.3.1_react-router@6.26.0__react@18.3.1_react-router-dom@6.26.0__react@18.3.1__react-dom@18.3.1___react@18.3.1", "ra-language-english": "ra-language-english@5.1.1_react@18.3.1_react-dom@18.3.1__react@18.3.1_react-hook-form@7.52.2__react@18.3.1_react-router@6.26.0__react@18.3.1_react-router-dom@6.26.0__react@18.3.1__react-dom@18.3.1___react@18.3.1", "ra-ui-materialui": "ra-ui-materialui@5.1.1_@mui+icons-material@5.16.6__@mui+material@5.16.6___@emotion+react@11.13.0____react@18.3.1___@emotion+styled@11.13.0____@emotion+react@11.13.0_____react@18.3.1____react@18.3.1___react@18.3.1___react-dom@18.3.1____react@18.3.1__react@18.3.1__@emotion+react@11.13.0___react@18.3.1__@emotion+styled@11.13.0___@emotion+react@11.13.0____react@18.3.1___react@18.3.1__react-dom@18.3.1___react@18.3.1_@mui+material@5.16.6__@emotion+react@11.13.0___react@18.3.1__@emotion+styled@11.13.0___@emotion+react@11.13.0____react@18.3.1___react@18.3.1__react@18.3.1__react-dom@18.3.1___react@18.3.1_ra-core@5.1.1__react@18.3.1__react-dom@18.3.1___react@18.3.1__react-hook-form@7.52.2___react@18.3.1__react-router@6.26.0___react@18.3.1__react-router-dom@6.26.0___react@18.3.1___react-dom@18.3.1____react@18.3.1_react@18.3.1_react-dom@18.3.1__react@18.3.1_react-hook-form@7.52.2__react@18.3.1_react-is@18.3.1_react-router@6.26.0__react@18.3.1_react-router-dom@6.26.0__react@18.3.1__react-dom@18.3.1___react@18.3.1_@emotion+react@11.13.0__react@18.3.1_@emotion+styled@11.13.0__@emotion+react@11.13.0___react@18.3.1__react@18.3.1", "react": "react@18.3.1", "react-dom": "react-dom@18.3.1_react@18.3.1", "react-hook-form": "react-hook-form@7.52.2_react@18.3.1", "react-router": "react-router@6.26.0_react@18.3.1", "react-router-dom": "react-router-dom@6.26.0_react@18.3.1_react-dom@18.3.1__react@18.3.1"}}, "react-dom@18.3.1_react@18.3.1": {"integrity": "sha512-5m4nQKp+rZRb09LNH59GM4BxTh9251/ylbKIbpe7TpGxfJ+9kv6BLkLBXIjjspbgbnIBNqlI23tRnTWT0snUIw==", "dependencies": {"loose-envify": "loose-envify@1.4.0", "react": "react@18.3.1", "scheduler": "scheduler@0.23.2"}}, "react-dropzone@14.2.3_react@18.3.1": {"integrity": "sha512-O3om8I+PkFKbxCukfIR3QAGftYXDZfOE2N1mr/7qebQJHs7U+/RSL/9xomJNpRg9kM5h9soQSdf0Gc7OHF5Fug==", "dependencies": {"attr-accept": "attr-accept@2.2.2", "file-selector": "file-selector@0.6.0", "prop-types": "prop-types@15.8.1", "react": "react@18.3.1"}}, "react-error-boundary@4.0.13_react@18.3.1": {"integrity": "sha512-b6PwbdSv8XeOSYvjt8LpgpKrZ0yGdtZokYwkwV2wlcZbxgopHX/hgPl5VgpnoVOWd868n1hktM8Qm4b+02MiLQ==", "dependencies": {"@babel/runtime": "@babel/runtime@7.25.0", "react": "react@18.3.1"}}, "react-hook-form@7.52.2_react@18.3.1": {"integrity": "sha512-pqfPEbERnxxiNMPd0bzmt1tuaPcVccywFDpyk2uV5xCIBphHV5T8SVnX9/o3kplPE1zzKt77+YIoq+EMwJp56A==", "dependencies": {"react": "react@18.3.1"}}, "react-is@16.13.1": {"integrity": "sha512-24e6ynE2H+OKt4kqsOvNd8kBpV65zoxbA4BVsEOB3ARVWQki/DHzaUoC5KuON/BiccDaCCTZBuOcfZs70kR8bQ==", "dependencies": {}}, "react-is@18.3.1": {"integrity": "sha512-/LLMVyas0ljjAtoYiPqYiL8VWXzUUdThrmU5+n20DZv+a+ClRoevUzw5JxU+Ieh5/c87ytoTBV9G1FiKfNJdmg==", "dependencies": {}}, "react-router-dom@6.26.0_react@18.3.1_react-dom@18.3.1__react@18.3.1": {"integrity": "sha512-RRGUIiDtLrkX3uYcFiCIxKFWMcWQGMojpYZfcstc63A1+sSnVgILGIm9gNUA6na3Fm1QuPGSBQH2EMbAZOnMsQ==", "dependencies": {"@remix-run/router": "@remix-run/router@1.19.0", "react": "react@18.3.1", "react-dom": "react-dom@18.3.1_react@18.3.1", "react-router": "react-router@6.26.0_react@18.3.1"}}, "react-router@6.26.0_react@18.3.1": {"integrity": "sha512-wVQq0/iFYd3iZ9H2l3N3k4PL8EEHcb0XlU2Na8nEwmiXgIUElEH6gaJDtUQxJ+JFzmIXaQjfdpcGWaM6IoQGxg==", "dependencies": {"@remix-run/router": "@remix-run/router@1.19.0", "react": "react@18.3.1"}}, "react-transition-group@4.4.5_react@18.3.1_react-dom@18.3.1__react@18.3.1": {"integrity": "sha512-pZcd1MCJoiKiBR2NRxeCRg13uCXbydPnmB4EOeRrY7480qNWO8IIgQG6zlDkm6uRMsURXPuKq0GWtiM59a5Q6g==", "dependencies": {"@babel/runtime": "@babel/runtime@7.25.0", "dom-helpers": "dom-helpers@5.2.1", "loose-envify": "loose-envify@1.4.0", "prop-types": "prop-types@15.8.1", "react": "react@18.3.1", "react-dom": "react-dom@18.3.1_react@18.3.1"}}, "react@18.3.1": {"integrity": "sha512-wS+hAgJShR0KhEvPJArfuPVN1+Hz1t0Y6n5jLrGQbkb4urgPE/0Rve+1kMB1v/oWgHgm4WIcV+i7F2pTVj+2iQ==", "dependencies": {"loose-envify": "loose-envify@1.4.0"}}, "regenerator-runtime@0.14.1": {"integrity": "sha512-dYnhHh0nJoMfnkZs6GmmhFknAGRrLznOu5nc9ML+EJxGvrx6H7teuevqVqCuPcPK//3eDrrjQhehXVx9cnkGdw==", "dependencies": {}}, "remove-accents@0.4.4": {"integrity": "sha512-EpFcOa/ISetVHEXqu+VwI96KZBmq+a8LJnGkaeFw45epGlxIZz5dhEEnNZMsQXgORu3qaMoLX4qJCzOik6ytAg==", "dependencies": {}}, "resolve-from@4.0.0": {"integrity": "sha512-pb/MYmXstAkysRFx8piNI1tGFNQIFA3vkE3Gq4EuA1dF6gHp/+vgZqsCGJapvy8N3Q+4o7FwvquPJcnZ7RYy4g==", "dependencies": {}}, "resolve@1.22.8": {"integrity": "sha512-oKWePCxqpd6FlLvGV1VU0x7bkPmmCNolxzjMf4NczoDnQcIWrAF+cPtZn5i6n+RfD2d9i0tzpKnG6Yk168yIyw==", "dependencies": {"is-core-module": "is-core-module@2.15.0", "path-parse": "path-parse@1.0.7", "supports-preserve-symlinks-flag": "supports-preserve-symlinks-flag@1.0.0"}}, "scheduler@0.23.2": {"integrity": "sha512-UOShsPwz7NrMUqhR6t0hWjFduvOzbtv7toDH1/hIrfRNIDBnnBWd0CwJTGvTpngVlmwGCdP9/Zl/tVrDqcuYzQ==", "dependencies": {"loose-envify": "loose-envify@1.4.0"}}, "set-function-length@1.2.2": {"integrity": "sha512-pgRc4hJ4/sNjWCSS9AmnS40x3bNMDTknHgL5UaMBTMyJnU90EgWh1Rz+MC9eFu4BuN/UwZjKQuY/1v3rM7HMfg==", "dependencies": {"define-data-property": "define-data-property@1.1.4", "es-errors": "es-errors@1.3.0", "function-bind": "function-bind@1.1.2", "get-intrinsic": "get-intrinsic@1.2.4", "gopd": "gopd@1.0.1", "has-property-descriptors": "has-property-descriptors@1.0.2"}}, "side-channel@1.0.6": {"integrity": "sha512-fDW/EZ6Q9RiO8eFG8Hj+7u/oW+XrPTIChwCOM2+th2A6OblDtYYIpve9m+KvI9Z4C9qSEXlaGR6bTEYHReuglA==", "dependencies": {"call-bind": "call-bind@1.0.7", "es-errors": "es-errors@1.3.0", "get-intrinsic": "get-intrinsic@1.2.4", "object-inspect": "object-inspect@1.13.2"}}, "source-map@0.5.7": {"integrity": "sha512-LbrmJOMUSdEVxIKvdcJzQC+nQhe8FUZQTXQy6+I75skNgn3OoQ0DZA8YnFa7gp8tqtL3KPf1kmo0R5DoApeSGQ==", "dependencies": {}}, "split-on-first@1.1.0": {"integrity": "sha512-43ZssAJaMusuKWL8sKUBQXHWOpq8d6CfN/u1p4gUzfJkM05C8rxTmYrkIPTXapZpORA6LkkzcUulJ8FqA7Uudw==", "dependencies": {}}, "strict-uri-encode@2.0.0": {"integrity": "sha512-QwiXZgpRcKkhTj2Scnn++4PKtWsH0kpzZ62L2R6c/LUVYv7hVnZqcg2+sMuT6R7Jusu1vviK/MFsu6kNJfWlEQ==", "dependencies": {}}, "stylis@4.2.0": {"integrity": "sha512-Orov6g6BB1sDfYgzWfTHDOxamtX1bE/zo104Dh9e6fqJ3PooipYyfJ0pUmrZO2wAvO8YbEyeFrkV91XTsGMSrw==", "dependencies": {}}, "supports-color@5.5.0": {"integrity": "sha512-QjVjwdXIt408MIiAqCX4oUKsgU2EqAGzs2Ppkm4aQYbjm+ZEWEcW4SfFNTr4uMNZma0ey4f5lgLrkB0aX0QMow==", "dependencies": {"has-flag": "has-flag@3.0.0"}}, "supports-preserve-symlinks-flag@1.0.0": {"integrity": "sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w==", "dependencies": {}}, "to-fast-properties@2.0.0": {"integrity": "sha512-/OaKK0xYrs3DmxRYqL/yDc+FxFUVYhDlXMhRmv3z915w2HF1tnN1omB354j8VUGO/hbRzyD6Y3sA7v7GS/ceog==", "dependencies": {}}, "tr46@0.0.3": {"integrity": "sha512-N3WMsuqV66lT30CrXNbEjx4GEwlow3v6rr4mCcv6prnfwhS01rkgyFdjPNBYd9br7LpXV1+Emh01fHnq2Gdgrw==", "dependencies": {}}, "tslib@2.6.3": {"integrity": "sha512-xNvxJEOUiWPGhUuUdQgAJPKOOJfGnIyKySOc09XkKsgdUV/3E2zvwZYdejjmRgPCgcym1juLH3226yA7sEFJKQ==", "dependencies": {}}, "undici-types@5.26.5": {"integrity": "sha512-JlCMO+ehdEIKqlFxk6IfVoAUVmgz7cU7zD/h9XZ0qzeosSHmUJVOzSQvvYSYWXkFXC+IfLKSIffhv0sVZup6pA==", "dependencies": {}}, "warning@4.0.3": {"integrity": "sha512-rpJyN222KWIvHJ/F53XSZv0Zl/accqHR8et1kpaMTD/fLCRxtV8iX8czMzY7sVZupTI3zcUTg8eycS2kNF9l6w==", "dependencies": {"loose-envify": "loose-envify@1.4.0"}}, "web-streams-polyfill@4.0.0-beta.3": {"integrity": "sha512-QW95TCTaHmsYfHDybGMwO5IJIM93I/6vTRk+daHTWFPhwh+C8Cg7j7XyKrwrj8Ib6vYXe0ocYNrmzY4xAAN6ug==", "dependencies": {}}, "webidl-conversions@3.0.1": {"integrity": "sha512-2JAn3z8AR6rjK8Sm8orRC0h/bcl/DqL7tRPdGZ4I1CjdF+EaMLmYxBHyXuKL849eucPFhvBoxMsflfOb8kxaeQ==", "dependencies": {}}, "whatwg-url@5.0.0": {"integrity": "sha512-saE57nupxk6v3HY35+jzBwYa0rKSy0XR8JSxZPwgLr7ys0IBzhGviA1/TUGJLmSVqs8pb9AnvICXEuOHLprYTw==", "dependencies": {"tr46": "tr46@0.0.3", "webidl-conversions": "webidl-conversions@3.0.1"}}, "ws@8.18.0": {"integrity": "sha512-8VbfWfHLbbwu3+N6OKsOMpBdT4kXPDDB9cJk2bJ6mh9ucxdlnNvH1e+roYkKmN9Nxw2yjz7VzeO9oOz2zJ04Pw==", "dependencies": {}}, "yaml@1.10.2": {"integrity": "sha512-r3vXyErRCYJ7wg28yvBY5VSoAF8ZvlcW9/BwUzEtUsjvX/DKs24dIkuwjtuprwJJHsbyUbLApepYTR1BN4uHrg==", "dependencies": {}}}}, "remote": {"https://deno.land/std@0.192.0/fmt/colors.ts": "d67e3cd9f472535241a8e410d33423980bec45047e343577554d3356e1f0ef4e", "https://deno.land/std@0.192.0/testing/_diff.ts": "1a3c044aedf77647d6cac86b798c6417603361b66b54c53331b312caeb447aea", "https://deno.land/std@0.192.0/testing/_format.ts": "a69126e8a469009adf4cf2a50af889aca364c349797e63174884a52ff75cf4c7", "https://deno.land/std@0.192.0/testing/asserts.ts": "e16d98b4d73ffc4ed498d717307a12500ae4f2cbe668f1a215632d19fcffc22f"}}